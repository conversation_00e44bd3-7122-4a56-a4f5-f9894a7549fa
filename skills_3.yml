menu_title: '&f:offset_-8::skills_3:'
open_commands:
  - '[sound] BLOCK_NOTE_BLOCK_PLING'
size: 54
update_interval: 1
items:
  '1':
    material: PAPER
    model_data: 1007
    display_name: '&a&lDefense &a%aureliumskills_defense_roman%'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Take damage from entities'
      - '&7to &f&nearn&7 Defense XP.'
      - ''
      - '&aStats Leveled:'
      - ' &5 ✦ &fᴛᴏᴜɢʜɴᴇss'
      - ' &c ❤ &fʜᴇᴀʟᴛʜ'
      - ''
      - '&aInformation:'
      - '  &f• &fʟᴇᴠᴇʟ: &a%aureliumskills_defense_roman%'
      - '  &f• &fᴘʀᴏɢʀᴇss: &d%aureliumskills_xp_int_defense%/%aureliumskills_xp_required_defense% &8(%aureliumskills_xp_progress_1_defense%%)'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view your progression.'
    left_click_commands:
      - '[player] defense'
    right_click_commands:
      - '[player] defense'
    slot: 19
  '2':
    material: PAPER
    model_data: 1008
    display_name: '&a&lFighting &a%aureliumskills_fighting_roman%'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Fight mobs with melee weapons'
      - '&7to &f&nearn&7 Fighting XP.'
      - ''
      - '&aStats Leveled:'
      - ' &4 ➽ &fsᴛʀᴇɴɢʜᴛ'
      - ' &6 ❥ &fʀᴇɢᴇɴᴇʀᴀᴛɪᴏɴ'
      - ''
      - '&aInformation:'
      - '  &f• &fʟᴇᴠᴇʟ: &a%aureliumskills_fighting_roman%'
      - '  &f• &fᴘʀᴏɢʀᴇss: &d%aureliumskills_xp_int_fighting%/%aureliumskills_xp_required_fighting% &8(%aureliumskills_xp_progress_1_fighting%%)'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view your progression.'
    left_click_commands:
      - '[player] fighting'
    right_click_commands:
      - '[player] fighting'
    slot: 22
  '3':
    material: PAPER
    model_data: 1009
    display_name: '&a&lEndurance &a%aureliumskills_endurance_roman%'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Walk and run to &f&nearn&7 Endurance XP.'
      - ''
      - '&aStats Leveled:'
      - ' &6 ❥ &fʀᴇɢᴇɴᴇʀᴀᴛɪᴏɴ'
      - ' &5 ✦ &fᴛᴏᴜɢʜɴᴇss'
      - ''
      - '&aInformation:'
      - '  &f• &fʟᴇᴠᴇʟ: &a%aureliumskills_endurance_roman%'
      - '  &f• &fᴘʀᴏɢʀᴇss: &d%aureliumskills_xp_int_endurance%/%aureliumskills_xp_required_endurance% &8(%aureliumskills_xp_progress_1_endurance%%)'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view your progression.'
    left_click_commands:
      - '[player] endurance'
    right_click_commands:
      - '[player] endurance'
    slot: 25
  'prev_page':
    material: MAP
    model_data: 1000
    slot: 36
    priority: 1
    update: true
    display_name: '&a&lPrevious Page'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[openguimenu] skills_2'
    right_click_commands:
      - '[openguimenu] skills_2'
  'next_page':
    material: MAP
    model_data: 1000
    slot: 44
    priority: 1
    update: true
    display_name: '&a&lNext Page'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[openguimenu] skills_4'
    right_click_commands:
      - '[openguimenu] skills_4'
  'skills':
    material: LEGACY_BOOK_AND_QUILL
    slot: 47
    priority: 1
    update: true
    display_name: '&a&lYour Skills'
    lore:
      - '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ'
      - ''
      - '&7Upgrade &b&lsᴋɪʟʟs &7by doing various'
      - '&7tasks to unlock valuable stat boosts,'
      - '&7abilities and more!'
      - ''
    left_click_commands:
      - '[none]'
    right_click_commands:
      - '[none]'
  'close':
    material: MAP
    model_data: 1000
    slot: 49
    priority: 1
    update: true
    display_name: '&c&lClose'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[close]'
    right_click_commands:
      - '[close]'
  'stats':
    material: head-%player_name%
    slot: 51
    priority: 1
    update: true
    display_name: '&a&lYour Stats'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Earn stat &dbuffs &7by'
      - '&7level up skills!'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view.'
    left_click_commands:
      - '[openguimenu] skills_stats_1'
    right_click_commands:
      - '[openguimenu] skills_stats_1'