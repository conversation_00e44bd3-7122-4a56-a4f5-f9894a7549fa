menu_title: '&f:offset_-8::skills_stats_2:'
open_commands:
  - '[sound] BLOCK_NOTE_BLOCK_PLING'
size: 54
update_interval: 1
items:
  '1':
    material: PAPER
    model_data: 1019
    display_name: '&2☘ &lLuck'
    lore:
      - '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ'
      - ''
      - '&7Luck increases your chances of'
      - '&7getting rare loot from mobs,'
      - '&7fishing and &amore&7.'
      - ''
      - '&7Your &f&ncurrent&7 level is &2&l%aureliumskills_luck_int%&7!'
      - ''
      - '&aSkills that apply:'
      - '&f  • ᴇɴᴄʜᴀɴᴛɪɴɢ'
      - '&f  • ғɪsʜɪɴɢ'
      - '&f  • ᴍɪɴɪɴɢ'
      - '&f  • ᴇxᴄᴀᴠᴀᴛɪᴏɴ'
      - '&f  • ᴀʀᴄʜᴇʀʏ'
      - ''
    left_click_commands:
      - '[none]'
    right_click_commands:
      - '[none]'
    slot: 19
  '2':
    material: PAPER
    model_data: 1020
    display_name: '&6❥ &lRegeneration'
    lore:
      - '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ'
      - ''
      - '&7Regeneration increases how fast'
      - '&7you recover both health and &bmana&7.'
      - ''
      - '&7Your &f&ncurrent&7 level is &6&l%aureliumskills_regeneration_int%&7!'
      - ''
      - '&aSkills that apply:'
      - '&f  • ғɪɢʜᴛɪɴɢ'
      - '&f  • ᴇɴᴅᴜʀᴀɴᴄᴇ'
      - '&f  • ʜᴇᴀʟɪɴɢ'
      - '&f  • ᴇxᴄᴀᴠᴀᴛɪᴏɴ'
      - '&f  • ᴀɢɪʟɪᴛʏ'
      - ''
    left_click_commands:
      - '[none]'
    right_click_commands:
      - '[none]'
    slot: 22
  '3':
    material: PAPER
    model_data: 1021
    display_name: '&9✿ &lWisdom'
    lore:
      - '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ'
      - ''
      - '&7Wisdom increases your max &bmana&7,'
      - '&7increases &aexperience&7 gain, and'
      - '&7decreases anvil costs.'
      - ''
      - '&7Your &f&ncurrent&7 level is &9&l%aureliumskills_wisdom_int%&7!'
      - ''
      - '&aSkills that apply:'
      - '&f  • ᴇɴᴄʜᴀɴᴛɪɴɢ'
      - '&f  • ᴀʟᴄʜᴇᴍʏ'
      - '&f  • sᴏʀᴄᴇʀʏ'
      - '&f  • ғᴏʀɢɪɴɢ'
      - '&f  • ᴀɢɪʟɪᴛʏ'
      - ''
    left_click_commands:
      - '[none]'
    right_click_commands:
      - '[none]'
    slot: 25
  'prev_page':
    material: MAP
    model_data: 1000
    slot: 36
    priority: 1
    update: true
    display_name: '&a&lPrevious Page'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[openguimenu] skills_stats_1'
    right_click_commands:
      - '[openguimenu] skills_stats_1'
  'back':
    material: MAP
    model_data: 1000
    slot: 49
    priority: 1
    update: true
    display_name: '&c&lBack'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[openguimenu] skills_1'
    right_click_commands:
      - '[openguimenu] skills_1'