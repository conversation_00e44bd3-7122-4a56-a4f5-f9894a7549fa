menu_title: '&f:offset_-8::skills_stats_1:'
open_command:
  - skills stats
  - skill stats
  - stats
  - statistics
open_commands:
  - '[sound] BLOCK_NOTE_BLOCK_PLING'
size: 54
update_interval: 1
items:
  '1':
    material: PAPER
    model_data: 1016
    display_name: '&4➽ &lStrength'
    lore:
      - '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ'
      - ''
      - '&7Strength increases your attack'
      - '&cdamage &7with various different'
      - '&7weapons.'
      - ''
      - '&7Your &f&ncurrent&7 level is &4&l%aureliumskills_strength_int%&7!'
      - ''
      - '&aSkills that apply:'
      - '&f  • ғᴏʀᴀɢɪɴɢ'
      - '&f  • ғɪɢʜᴛɪɴɢ'
      - '&f  • sᴏʀᴄᴇʀʏ'
      - '&f  • ғᴀʀᴍɪɴɢ'
      - '&f  • ᴀʀᴄʜᴇʀʏ'
      - ''
    left_click_commands:
      - '[none]'
    right_click_commands:
      - '[none]'
    slot: 19
  '2':
    material: PAPER
    model_data: 1017
    display_name: '&5✦ &lToughness'
    lore:
      - '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ'
      - ''
      - '&7Toughness increases the amount of'
      - '&cdamage &7reduced from enemy attacks.'
      - ''
      - '&7Your &f&ncurrent&7 level is &5&l%aureliumskills_toughness_int%&7!'
      - ''
      - '&aSkills that apply:'
      - '&f  • ғᴏʀᴀɢɪɴɢ'
      - '&f  • ᴍɪɴɪɴɢ'
      - '&f  • ᴇɴᴅᴜʀᴀɴᴄᴇ'
      - '&f  • ᴅᴇғᴇɴsᴇ'
      - '&f  • ғᴏʀɢɪɴɢ'
      - ''
    left_click_commands:
      - '[none]'
    right_click_commands:
      - '[none]'
    slot: 22
  '3':
    material: PAPER
    model_data: 1018
    display_name: '&c❤ &lHealth'
    lore:
      - '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ'
      - ''
      - '&7Health increases the amount of'
      - '&cHP &7you have, allowing you to'
      - '&7last longer in fights.'
      - ''
      - '&7Your &f&ncurrent&7 level is &c&l%aureliumskills_health_int%&7!'
      - ''
      - '&aSkills that apply:'
      - '&f  • ғɪsʜɪɴɢ'
      - '&f  • ᴀʟᴄʜᴇᴍʏ'
      - '&f  • ʜᴇᴀʟɪɴɢ'
      - '&f  • ᴅᴇғᴇɴsᴇ'
      - '&f  • ғᴀʀᴍɪɴɢ'
      - ''
    left_click_commands:
      - '[none]'
    right_click_commands:
      - '[none]'
    slot: 25
  'next_page':
    material: MAP
    model_data: 1000
    slot: 44
    priority: 1
    update: true
    display_name: '&a&lNext Page'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[openguimenu] skills_stats_2'
    right_click_commands:
      - '[openguimenu] skills_stats_2'
  'back':
    material: MAP
    model_data: 1000
    slot: 49
    priority: 1
    update: true
    display_name: '&c&lBack'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[openguimenu] skills_1'
    right_click_commands:
      - '[openguimenu] skills_1'