menu_title: '&f:offset_-8::skills_5:'
open_commands:
  - '[sound] BLOCK_NOTE_BLOCK_PLING'
size: 54
update_interval: 1
items:
  '1':
    material: PAPER
    model_data: 1013
    display_name: '&a&lSorcery &a%aureliumskills_sorcery_roman%'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Use &bmana abilities &7to'
      - '&f&nearn&7 Sorcery XP.'
      - ''
      - '&aStats Leveled:'
      - ' &4 ➽ &fsᴛʀᴇɴɢʜᴛ'
      - ' &9 ✿ &fᴡɪsᴅᴏᴍ'
      - ''
      - '&aInformation:'
      - '  &f• &fʟᴇᴠᴇʟ: &a%aureliumskills_sorcery_roman%'
      - '  &f• &fᴘʀᴏɢʀᴇss: &d%aureliumskills_xp_int_sorcery%/%aureliumskills_xp_required_sorcery% &8(%aureliumskills_xp_progress_1_sorcery%%)'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view your progression.'
    left_click_commands:
      - '[player] sorcery'
    right_click_commands:
      - '[player] sorcery'
    slot: 19
  '2':
    material: PAPER
    model_data: 1014
    display_name: '&a&lHealing &a%aureliumskills_healing_roman%'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Drink and splash potions'
      - '&7to &f&nearn&7 Healing XP.'
      - ''
      - '&aStats Leveled:'
      - ' &6 ❥ &fʀᴇɢᴇɴᴇʀᴀᴛɪᴏɴ'
      - ' &c ❤ &fʜᴇᴀʟᴛʜ'
      - ''
      - '&aInformation:'
      - '  &f• &fʟᴇᴠᴇʟ: &a%aureliumskills_healing_roman%'
      - '  &f• &fᴘʀᴏɢʀᴇss: &d%aureliumskills_xp_int_healing%/%aureliumskills_xp_required_healing% &8(%aureliumskills_xp_progress_1_healing%%)'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view your progression.'
    left_click_commands:
      - '[player] healing'
    right_click_commands:
      - '[player] healing'
    slot: 22
  '3':
    material: PAPER
    model_data: 1015
    display_name: '&a&lForging &a%aureliumskills_forging_roman%'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Combine and apply books in an'
      - '&7anvil to &f&nearn&7 Forging XP.'
      - ''
      - '&aStats Leveled:'
      - ' &5 ✦ &fᴛᴏᴜɢʜɴᴇss'
      - ' &9 ✿ &fᴡɪsᴅᴏᴍ'
      - ''
      - '&aInformation:'
      - '  &f• &fʟᴇᴠᴇʟ: &a%aureliumskills_forging_roman%'
      - '  &f• &fᴘʀᴏɢʀᴇss: &d%aureliumskills_xp_int_forging%/%aureliumskills_xp_required_forging% &8(%aureliumskills_xp_progress_1_forging%%)'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view your progression.'
    left_click_commands:
      - '[player] forging'
    right_click_commands:
      - '[player] forging'
    slot: 25
  'prev_page':
    material: MAP
    model_data: 1000
    slot: 36
    priority: 1
    update: true
    display_name: '&a&lPrevious Page'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[openguimenu] skills_4'
    right_click_commands:
      - '[openguimenu] skills_4'
  'skills':
    material: LEGACY_BOOK_AND_QUILL
    slot: 47
    priority: 1
    update: true
    display_name: '&a&lYour Skills'
    lore:
      - '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ'
      - ''
      - '&7Upgrade &b&lsᴋɪʟʟs &7by doing various'
      - '&7tasks to unlock valuable stat boosts,'
      - '&7abilities and more!'
      - ''
    left_click_commands:
      - '[none]'
    right_click_commands:
      - '[none]'
  'close':
    material: MAP
    model_data: 1000
    slot: 49
    priority: 1
    update: true
    display_name: '&c&lClose'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[close]'
    right_click_commands:
      - '[close]'
  'stats':
    material: head-%player_name%
    slot: 51
    priority: 1
    update: true
    display_name: '&a&lYour Stats'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Earn stat &dbuffs &7by'
      - '&7level up skills!'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view.'
    left_click_commands:
      - '[openguimenu] skills_stats_1'
    right_click_commands:
      - '[openguimenu] skills_stats_1'