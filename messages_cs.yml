# Aurelium Skills český překlad
abilities:
  farming:
    bountiful_harvest:
      name: 'ʙᴏʜᴀᴛᴀ sᴋʟɪᴢᴇɴ'
      desc: '&a{value}% &7šance získat &f&ndvojnásobné&7 výnosy\n&7   z plodin.'
      info: '{value}% 2x Výnosy'
    farmer:
      name: 'ғᴀʀᴍᴀʀ'
      desc: 'Získávej &a{value}% &7více Farmářských XP.'
      info: '+{value}% XP'
    scythe_master:
      name: 'ᴍɪsᴛʀ ᴋᴏsʏ'
      desc: 'Z<PERSON><PERSON><PERSON><PERSON> poškození od motyk o &a{value}%&7.'
      info: '+{value}% Poškození Motyk'
    geneticist:
      name: 'ɢᴇɴᴇᴛɪᴋ'
      desc: 'Zvyšuje nasycení z\n&7   rostlinných potravin o &a{value}&7.'
      info: '+{value} Nasycení'
    triple_harvest:
      name: 'ᴛʀᴏᴊɴᴀ sᴋʟɪᴢᴇɴ'
      desc: '&a{value}%&7 šance získat trojnásobné výnosy z\n&7   plodin.'
      info: '{value}% 3x Výnosy'
  foraging:
    lumberjack:
      name: 'ᴅʀᴇᴠᴏʀᴜʙᴇᴄ'
      desc: '&a{value}%&7 šance získat &f&ndvojnásobné&7 výnosy\n&7   z kmenů.'
      info: '{value}% 2x Výnosy'
    forager:
      name: 'sʙᴇʀᴀᴄ'
      desc: 'Získávej &a{value}% &7více Sběračských XP.'
      info: '+{value}% XP'
    axe_master:
      name: 'ᴍɪsᴛʀ sᴇᴋᴇʀʏ'
      desc: 'Zvyšuje poškození od seker o &a{value}%&7.'
      info: '+{value}% Poškození Seker'
    valor:
      name: 'ʜʀᴅɪɴsᴛᴠɪ'
      desc: 'Uděluje &4+{value} Sílu&7 při držení\n&7   sekery.'
      info: '+{value} Síla'
    shredder:
      name: 'ᴅʀᴛɪᴄ'
      desc: '&a{value}%&7 šance způsobit &f&ntrojnásobné&7 poškození\n&7   trvanlivosti sekerami.'
      info: '{value}% 3x Poškození Trvanlivosti'
  mining:
    lucky_miner:
      name: 'sᴛᴀsᴛɴʏ ʜᴏʀɴɪᴋ'
      desc: '&a{value}%&7 šance získat &f&ndvojnásobné&7 výnosy\n&7   z rud.'
      info: '{value}% 2x Výnosy'
    miner:
      name: 'ʜᴏʀɴɪᴋ'
      desc: 'Získávej &a{value}%&7 více Hornických XP.'
      info: '+{value}% XP'
    pick_master:
      name: 'ᴍɪsᴛʀ ᴋʀᴜᴍᴘᴀᴄᴇ'
      desc: 'Způsobuj &a{value}%&7 více poškození krumpáči.'
      info: '+{value}% Poškození Krumpáče'
    stamina:
      name: 'ᴠʏᴛʀᴠᴀʟᴏsᴛ'
      desc: 'Uděluje &5+{value} Odolnost&7 při\n&7   držení krumpáče'
      info: '+{value} Odolnost'
    hardened_armor:
      name: 'ᴢᴛᴠʀᴅʟᴇ ʙʀɴᴇɴɪ'
      desc: '&a{value}%&7 šance &f&nzabránit&7 poškození\n&7   trvanlivosti brnění.'
      info: '{value}% Bez Ztráty Trvanlivosti'
  fishing:
    lucky_catch:
      name: 'sᴛᴀsᴛɴʏ ᴢᴀᴄʜʏᴛ'
      desc: '&a{value}% &7přidaná šance získat &f&ndvojnásobné&7 výnosy\n&7   z rybaření.'
      info: '{value}% 2x Výnosy'
    fisher:
      name: 'ʀʏʙᴀʀ'
      desc: 'Získávej &a{value}%&7 více Rybářských XP.'
      info: '+{value}% XP'
    treasure_hunter:
      name: 'ʟᴏᴠᴇᴄ ᴘᴏᴋʟᴀᴅᴜ'
      desc: '&a{value}%&7 vyšší šance získat vzácný loot z rybaření.'
      info: '+{value}% Vzácný Loot'
    grappler:
      name: 'ʜᴀᴋᴏᴠɴɪᴋ'
      desc: 'Zachyť entity s &a{value}%&7 vyšší rychlostí.'
      info: '+{value}% Rychlost Háčku'
    epic_catch:
      name: 'ᴇᴘɪᴄᴋʏ ᴢᴀᴄʜʏᴛ'
      desc: '&a{value}%&7 přidaná šance získat epický loot\n&7   z rybaření.'
      info: '+{value}% Epický Loot'
  excavation:
    metal_detector:
      name: 'ᴅᴇᴛᴇᴋᴛᴏʀ ᴋᴏᴠᴜ'
      desc: '&a{value}%&7 přidaná šance získat vzácný loot z kopání.'
      info: '+{value}% Vzácný Loot'
    excavator:
      name: 'ᴇxᴋᴀᴠᴀᴛᴏʀ'
      desc: 'Získávej &a{value}%&7 více Vykopávkových XP.'
      info: '+{value}% XP'
    spade_master:
      name: 'ᴍɪsᴛʀ ʟᴏᴘᴀᴛʏ'
      desc: 'Způsobuj &a{value}%&7 více poškození lopatami.'
      info: '+{value}% Poškození Lopat'
    bigger_scoop:
      name: 'ᴠᴇᴛsɪ ɴᴀʙᴇʀᴀᴄᴋᴀ'
      desc: '&a{value}%&7 šance získat &f&ntrojnásobné&7 výnosy z kopání.'
      info: '{value}% 3x Výnosy'
    lucky_spades:
      name: 'sᴛᴀsᴛɴᴇ ʟᴏᴘᴀᴛʏ'
      desc: '&a{value}%&7 přidaná šance získat epický loot z kopání.'
      info: '+{value}% Epický Loot'
  archery:
    crit_chance:
      name: 'sᴀɴᴄᴇ ᴋʀɪᴛɪᴄᴋᴇʜᴏ'
      desc: '&a+{value}% &7šance způsobit &4kritický&7 zásah.'
      info: '+{value}% Šance Kritu'
    archer:
      name: 'ʟᴜᴄɪsᴛɴɪᴋ'
      desc: 'Získávej &a{value}%&7 více Lukostřeleckých XP.'
      info: '+{value}% XP'
    bow_master:
      name: 'ᴍɪsᴛʀ ʟᴜᴋᴜ'
      desc: 'Způsobuj &a{value}%&7 více poškození luky.'
      info: '+{value}% Poškození Luku'
    piercing:
      name: 'ᴘʀᴏʙᴏᴅᴇɴɪ'
      desc: '&a{value}%&7 šance, že šípy\n&7   proniknou skrz moby.'
      info: '{value}% Šance Proniknutí'
    stun:
      name: 'ᴏᴍʀᴀᴄᴇɴɪ'
      desc: '&a{value}% &7šance &csnížit&7 rychlost pohybu\n&7   nepřítele o &a20%&7 na 2 sekundy.'
      info: '{value}% Šance Omráčení'
  defense:
    shielding:
      name: 'sᴛɪᴛᴇɴɪ'
      desc: 'Sniž příchozí poškození o &a{value}%\n&7   při plížení.'
      info: '-{value}% Příchozí Poškození při Plížení'
    defender:
      name: 'ᴏʙʀᴀɴᴄᴇ'
      desc: 'Získávej {value}% více Obranných XP'
      info: '+{value}% XP'
    mob_master:
      name: 'ᴍɪsᴛʀ ᴍᴏʙᴜ'
      desc: 'Sniž příchozí poškození od mobů o &a{value}%&7.'
      info: '-{value}% Poškození od Mobů'
    immunity:
      name: 'ɪᴍᴜɴɪᴛᴀ'
      desc: '{value}% šance úplně zablokovat\n&7   nepřátelský útok.'
      info: '{value}% Imunita'
    no_debuff:
      name: 'ʙᴇᴢ ᴅᴇʙᴜғғᴜ'
      desc: '&a{value}%&7 šance zablokovat škodlivý\n&7   lektvarový efekt.'
      info: '{value}% Bez Debuffu'
  fighting:
    crit_damage:
      name: 'ᴋʀɪᴛɪᴄᴋᴇ ᴘᴏsᴋᴏᴢᴇɴɪ'
      desc: 'Zvyšuje &4kritické&7 poškození o &a{value}%&7'
      info: '+{value}% Kritické Poškození'
    fighter:
      name: 'ʙᴏᴊᴏᴠɴɪᴋ'
      desc: 'Získávej &a{value}%&7 více Bojových XP.'
      info: '+{value}% XP'
    sword_master:
      name: 'ᴍɪsᴛʀ ᴍᴇᴄᴇ'
      desc: 'Způsobuj &a{value}%&7 více poškození meči.'
      info: '+{value}% Poškození Meče'
    first_strike:
      name: 'ᴘʀᴠɴɪ ᴜᴅᴇʀ'
      desc: 'Způsobuj &a{value}%&7 více poškození při prvním zásahu.'
      info: '+{value}% Poškození První Zásah'
      dealt: '&cPrvní Úder Zasažen!'
    bleed:
      name: 'ᴋʀᴠᴀᴄᴇɴɪ'
      desc: '&a{value}% &7šance způsobit nepříteli &4krvácení&7 na\n&7   3 kola, způsobující &c{value_2}&7 poškození za kolo.'
      info: '{value}% Šance, {value_2} Poškození'
      enemy_bleeding: '&cNepřítel Krvácí!'
      self_bleeding: '&cKrvácíš!'
      stop: '&fKrvácení se zastavilo'
  endurance:
    anti_hunger:
      name: 'ᴀɴᴛɪ ʜʟᴀᴅ'
      desc: '&a{value}%&7 šance zastavit ztrátu hladu.'
      info: '{value}% Bez Ztráty Hladu'
    runner:
      name: 'ʙᴇʜᴇᴄ'
      desc: 'Získávej &a{value}%&7 více Vytrvalostních XP.'
      info: '+{value}% XP'
    golden_heal:
      name: 'ᴢʟᴀᴛᴇ ʟᴇᴄᴇɴɪ'
      desc: 'Zvyšuje efekt regenerace\n&7   zdraví o {value}%'
      info: '+{value}% Efekt Regenerace'
    recovery:
      name: 'ᴢᴏᴛᴀᴠᴇɴɪ'
      desc: 'Zvyšuje přirozenou regeneraci při\n&7   méně než polovině zdraví o &a{value}%&7.'
      info: '+{value}% Regenerace při Nízkém HP'
    meal_steal:
      name: 'ᴋʀᴀᴅᴇᴢ ᴊɪᴅʟᴀ'
      desc: '&a{value}%&7 šance ukrást 1 bod hladu\n&7   při útoku na hráče.'
      info: '{value}% Krádež Hladu'
  agility:
    light_fall:
      name: 'ʟᴇʜᴋʏ ᴘᴀᴅ'
      desc: 'Snižuje veškeré poškození z pádu o &a{value}%&7.'
      info: '-{value}% Poškození z Pádu'
    jumper:
      name: 'sᴋᴀᴋᴀɴ'
      desc: 'Získávej &a{value}% &7více Obratnostních XP.'
      info: '+{value}% XP'
    sugar_rush:
      name: 'ᴄᴜᴋʀᴏᴠʏ ʀᴜsʜ'
      desc: 'Konzumované a rozstříkané lektvary rychlosti a skoku\n&7   vydrží &a{value}%&7 déle.'
      info: '{value}% Delší Rychlost'
    fleeting:
      name: 'ᴜᴛᴇᴋᴀᴊɪᴄɪ'
      desc: 'Získej &a+{value}%&7 rychlost pohybu pod &c20% zdraví&7.'
      info: '+{value}% Rychlost'
      start: '&fZískal jsi dočasné zrychlení! &7(+{value}% Rychlost)'
      end: '&7Dočasné zrychlení pominulo'
    thunder_fall:
      name: 'ʜʀᴏᴍᴏᴠʏ ᴘᴀᴅ'
      desc: 'Při plížení během pádu máš &a{value}%&7 šanci\n&7   způsobit &a{value_2}%&7 očekávaného poškození z pádu mobům\n&7   v okruhu 3 bloků.'
      info: '{value}% Šance, {value_2}% Poškození'
  alchemy:
    alchemist:
      name: 'ᴀʟᴄʜᴇᴍɪsᴛᴀ'
      desc: 'Lektvary, které uvařís, mají &a{value}%&7 delší\n&7   trvání.'
      info: '+{value}% Trvání Lektvarů'
      lore: '&a(+{duration})'
    brewer:
      name: 'ᴠᴀʀɪᴄ'
      desc: 'Získávej &a{value}%&7 více Alchymistických XP'
      info: '+{value}% XP'
    splasher:
      name: 'ʀᴏᴢsᴛʀɪᴋᴏᴠᴀᴄ'
      desc: 'Lektvary, které rozstříkáš, získají &a{value}%&7 delší\n&7   trvání za každého zasaženého hráče.'
      info: '+{value}% Trvání/Hráč'
    lingering:
      name: 'ᴢᴜsᴛᴀᴠᴀᴊɪᴄɪ'
      desc: 'Přetrvávající lektvary se přirozeně rozpadají &a{value}%\n&7   pomaleji a rozpadají se &a{value_2}% &7méně za zasaženou entitu.'
      info: '-{value}% Přirozený, -{value_2}% Rozpad Entity'
    wise_effect:
      name: 'ᴍᴜᴅʀʏ ᴇғᴇᴋᴛ'
      desc: 'Získej &9{value} Moudrost &7za každý unikátní aktivní\n&7   lektvarový efekt.'
      info: '+{value} Moudrost/Efekt'
  enchanting:
    xp_convert:
      name: 'xᴘ ᴋᴏɴᴠᴇʀᴢᴇ'
      desc: 'Získej 1 Minecraft zkušenost za každých &d{value}\n&7   skill XP, které získáš.'
      info: '1 EXP/{value} Skill XP'
    enchanter:
      name: 'ᴇɴᴄʜᴀɴᴛᴇʀ'
      desc: 'Získávej &a{value}% &7více Enchantovacích XP.'
      info: '+{value}% XP'
    xp_warrior:
      name: 'xᴘ ʙᴏᴊᴏᴠɴɪᴋ'
      desc: 'Mobové, které zabiješ, mají &a{value}%&7 šanci\n&7   dropnout dvojnásobnou zkušenost.'
      info: '{value}% 2x Mob XP'
    enchanted_strength:
      name: 'ᴇɴᴄʜᴀɴᴛᴏᴠᴀɴᴀ sɪʟᴀ'
      desc: 'Získej &4{value} Sílu&7 za každý unikátní\n&7   enchant na drženém předmětu.'
      info: '{value} Síla/Enchant'
    lucky_table:
      name: 'sᴛᴀsᴛɴʏ sᴛᴜʟ'
      desc: 'Předměty, které enchantneš pomocí enchantovacího\n&7   stolu, mají &a{value}%&7 šanci upgradnout úroveň enchantu\n&7   o 1, pokud není na maximu.'
      info: '{value}% Šance Upgrade Enchantu'
  healing:
    life_essence:
      name: 'ᴢɪᴠᴏᴛɴɪ ᴇsᴇɴᴄᴇ'
      desc: 'Lektvary okamžitého léčení léčí &a{value}% &7více.'
      info: '+{value}% Okamžité Léčení'
    healer:
      name: 'ʟᴇᴄɪᴛᴇʟ'
      desc: 'Získávej &a{value}%&7 více Léčitelských XP.'
      info: '+{value}% XP'
    life_steal:
      name: 'ᴋʀᴀᴅᴇᴢ ᴢɪᴠᴏᴛᴀ'
      desc: 'Vyléč si &a{value}%&7 z maximálního HP nepřátelských mobů\n&7  a hráčů, které zabiješ.'
      info: 'Ukradni {value}% HP'
    golden_heart:
      name: 'ᴢʟᴀᴛᴇ sʀᴅᴄᴇ'
      desc: 'Poškození způsobené tvým absorpčním srdcím je\n&7   sníženo o &a{value}%&7.'
      info: '-{value}% Poškození Absorpce'
    revival:
      name: 'ᴏʙʀᴏᴅᴀ'
      desc: 'Získej &c+{value} Zdraví &7a &6+{value_2} Regeneraci\n&7   na 30 sekund po respawnu.'
      info: '+{value} Zdraví, +{value_2} Regenerace'
      message: '&fZískal jsi bonus obrody na 30 sekund! &f(&c+{value} Zdraví, &6+{value_2} Regenerace&f)'
  forging:
    disenchanter:
      name: 'ᴅɪsᴇɴᴄʜᴀɴᴛᴇʀ'
      desc: Získej &a{value}%&7 více zkušeností při disenchantování\n&7   předmětů pomocí brousícího kamene.
      info: '+{value}% Zkušenost z Brousícího Kamene'
    forger:
      name: 'ᴋᴏᴠᴀʀ'
      desc: Získávej &a{value}% &7více Kovářských XP.
      info: '+{value}% XP'
    repairing:
      name: 'ᴏᴘʀᴀᴠᴏᴠᴀɴɪ'
      desc: Opravování předmětů v kovadlině pomocí surového\n&7   materiálu, ze kterého byl předmět vyroben, opraví\n   &a{value}%&7 více trvanlivosti.
      info: '+{value}% Oprava Kovadlinou'
    anvil_master:
      name: 'ᴍɪsᴛʀ ᴋᴏᴠᴀᴅʟɪɴʏ'
      desc: Zvyšuje maximální cenu opravy kovadliny\n&7   před tím, než je příliš drahá na &a{value} zkušeností&7.
      info: '{value} Max Cena'
    skill_mender:
      name: 'sᴋɪʟʟ ᴏᴘʀᴀᴠᴀʀ'
      desc: Získávání skill XP má &a{value}%&7 šanci opravit\n&7   držený předmět nebo nošený kus brnění s Mending\n&7   enchantem o 1 trvanlivost za 2 skill XP.
      info: '{value}% Šance Opravy'
acf:
  core:
    permission_denied: '&8[&a&l!&8] &7Promiň, ale nemáš oprávnění k provedení tohoto příkazu.'
    permission_denied_parameter: '&8[&a&l!&8] &7Promiň, ale nemáš oprávnění k provedení tohoto příkazu.'
    error_generic_logged: '&8[&a&l!&8] &7Došlo k chybě. Tento problém byl zaznamenán. Omlouváme se za nepříjemnosti.'
    unknown_command: '&8[&a&l!&8] &7Neznámý příkaz, prosím napiš &a/sk help&7.'
    invalid_syntax: '&8[&a&l!&8] &7Použití: &a{command} {syntax}&7.'
    error_prefix: '{message}'
    error_performing_command: "&8[&a&l!&8] &7Promiň, ale došlo k chybě při provádění tohoto příkazu."
    info_message: '{message}'
    please_specify_one_of: '&8[&a&l!&8] &7Prosím specifikuj jeden z &a{valid}&7.'
    must_be_a_number: '&8[&a&l!&8] &a{num}&7 musí být číslo.'
    must_be_min_length: '&8[&a&l!&8] &7Musí mít alespoň &a{min} &7znaků.'
    must_be_max_length: '&8[&a&l!&8] &7Musí mít nejvýše &a{max} &7znaků.'
    please_specify_at_most: '&8[&a&l!&8] &7Prosím specifikuj hodnotu nejvýše &a{max}&7.'
    please_specify_at_least: '&8[&a&l!&8] &7Prosím specifikuj hodnotu alespoň &a{min}&7.'
    not_allowed_on_console: '&8[&a&l!&8] &7Konzole nemůže provést tento příkaz.'
    could_not_find_player: '&8[&a&l!&8] &7Nelze najít hráče se jménem: &e{search}'
    no_command_matched_search: '&8[&a&l!&8] &7Žádný příkaz neodpovídá &a{search}&7.'
    help_page_information: '&b - Zobrazuji stránku &a{page} &bz &a{totalpages} &b(&e{results}&b výsledků).'
    help_no_results: '&bChyba: Žádné další výsledky.'
    help_header: '&e=== &bZobrazuji nápovědu pro &a{commandprefix}{command}&e ==='
    help_format: '&b{commandprefix}{command} &a{parameters} &e{separator} {description}'
    help_detailed_header: '&e=== &bZobrazuji detailní nápovědu pro &a{commandprefix}{command}&e ==='
    help_detailed_command_format: '&b{command} &a{parameters} &e{separator} {description}'
    help_detailed_parameter_format: '&a{syntaxorname}: &e{description}'
    help_search_header: '&e=== &bVýsledky hledání pro &a{commandprefix}{command} {search}&e ==='
  minecraft:
    invalid_world: '&8[&a&l!&8] &7Tento svět neexistuje.'
    you_must_be_holding_item: '&8[&a&l!&8] &7Musíš držet předmět v hlavní ruce.'
    player_is_vanished_confirm: '&8[&a&l!&8] &a{vanished} &7je neviditelný. Neprozraď ho!\n&fPro potvrzení akce přidej &a:confirm &7na konec jeho jména.\n&ePříklad: {vanished}:confirm'
    username_too_short: '&8[&a&l!&8] &7Uživatelské jméno příliš krátké, musí mít alespoň tři znaky.'
    is_not_a_valid_name: '&8[&a&l!&8] &a{name} &7není platné uživatelské jméno.'
    multiple_players_match: '&8[&a&l!&8] &7Více hráčů odpovídá &b{search} &7({all})&7, buď prosím konkrétnější.'
    no_player_found_server: '&8[&a&l!&8] &7Žádný hráč odpovídající &e{search} &7není připojen k tomuto serveru.'
    no_player_found_offline: '&8[&a&l!&8] &7Žádný hráč odpovídající &e{search}&7 nebyl nalezen.'
    no_player_found: '&8[&a&l!&8] &7Žádný hráč odpovídající &e{search}&7 nebyl nalezen.'
    location_please_specify_world: '&8[&a&l!&8] &7Prosím specifikuj svět. Příklad: &eworld:x,y,z&7.'
    location_please_specify_xyz: '&8[&a&l!&8] &7Prosím specifikuj souřadnice x, y a z. Příklad: &eworld:x,y,z&7.'
    location_console_not_relative: '&8[&a&l!&8] &7Konzole nemůže použít relativní souřadnice pro lokaci.'
action_bar:
  idle: '&c{hp}/{max_hp}❤                 &b{mana}/{max_mana}☄ {mana_unit}'
  xp: '&c{hp}/{max_hp}❤  &a+{xp_gained} {skill} {xp_unit} &7({current_xp}/{level_xp} {xp_unit})  &b{mana}/{max_mana} {mana_unit}'
  xp_removed: '&c{hp}/{max_hp}❤  &a-{xp_removed} {skill} {xp_unit} &7({current_xp}/{level_xp} {xp_unit})  &b{mana}/{max_mana} {mana_unit}'
  maxed: '&c{hp}/{max_hp}❤  &a+{xp_gained} {skill} {xp_unit} &c(MAXIMUM!)  &b{mana}/{max_mana} {mana_unit}'
  maxed_removed: '&c{hp}/{max_hp}❤  &a-{xp_removed} {skill} {xp_unit} &c(MAXIMUM!)  &b{mana}/{max_mana} {mana_unit}'
  ability: '{message}'
  boss_bar_xp: '&a{skill} {level} &7({current_xp}/{level_xp} {xp_unit})'
  boss_bar_maxed: '&a{skill} {level} &c(MAXIMUM!)'
commands:
  prefix: '&8[&a&l!&8] &7'
  armor:
    modifier:
      add:
        added: '&a{stat} &7modifikátor s úrovní &b{value} &7byl přidán k tvému předmětu.'
        already_exists: '&7Modifikátor brnění se statistikou &a{stat} &7již na tvém brnění existuje! Nejdříve ho odstraň pomocí &a/sk armor modifier remove&7.'
        lore: '&7{stat}: {color}+{value}'
        lore_subtract: '&7{stat}: {color}-{value}'
      remove:
        does_not_exist: '&7Tvůj předmět nemá &a{stat} &7modifikátor!'
        removed: '&a{stat} &7modifikátor byl odstraněn z tvého předmětu.'
      list:
        header: '&8[&a&l!&8] &7Nalezené modifikátory brnění:'
        entry: '  &f• &f{stat}: &a{value}'
      removeall:
        removed: '&7Všechny modifikátory brnění byly odstraněny z tvého předmětu.'
    multiplier:
      add:
        added: '&a{target} &7multiplikátor s hodnotou &b{value} &7byl přidán k tvému předmětu.'
        already_exists: '&7Multiplikátor brnění s cílem &a{target} &7již na tvém brnění existuje! Nejdříve ho odstraň pomocí &a/sk armor multiplier remove&7.'
        skill_lore: '&7Při nošení: &a+{value}% {skill} XP.'
        skill_lore_subtract: '&7Při nošení: &a-{value}% {skill} XP.'
        global_lore: '&7Při nošení: &9+{value}% Skill XP.'
        global_lore_subtract: '&7Při nošení: &9-{value}% Skill XP.'
      remove:
        does_not_exist: '&7Tvůj předmět nemá &a{target} &7multiplikátor brnění!'
        removed: '&a{target} &7multiplikátor brnění byl odstraněn z tvého předmětu.'
      list:
        header: '&8[&a&l!&8] &7Nalezené multiplikátory brnění:'
        entry: '  &f• &f{target}: &a{value}'
      removeall:
        removed: '&7Všechny multiplikátory brnění byly odstraněny z tvého předmětu.'
    requirement:
      add:
        added: '&a{skill}&7 požadavek brnění s úrovní &b{level}&7 byl přidán k tvému předmětu.'
        already_exists: '&7Předmět již má &a{skill}&7 požadavek brnění! Nejdříve ho odstraň pomocí &a/sk armor requirement remove&7.'
        lore: '&7Vyžaduje &a{skill} {level}&7.'
      remove:
        does_not_exist: '&7Tvůj předmět nemá &a{skill}&7 požadavek brnění!'
        removed: '&a{skill}&7 požadavek brnění byl odstraněn z tvého předmětu'
      list:
        header: '&8[&a&l!&8] &7Nalezené požadavky brnění:'
        entry: '  &f• &f{skill}: &a{level}'
      removeall:
        removed: '&7Všechny požadavky brnění byly odstraněny z tvého předmětu'
      equip: '&7Nesplňuješ požadavky pro nasazení tohoto předmětu: {requirements}'
      entry: '&a{skill} {level}&7, '
  backup:
    load:
      confirm: '&7Načtení zálohy vrátí všechna skill data hráčů. Tato akce je trvalá a nelze ji vrátit zpět. Možná si budeš chtít uložit zálohu před načtením jiné. Napiš příkaz znovu pro potvrzení této akce'
      loading: '&7Načítám zálohu...'
      loaded: '&7Záloha úspěšně načtena'
      error: '&7Chyba při načítání zálohy: &c{error}'
      must_be_yaml: '&7Soubor musí být &a.yml&7 soubor!'
      file_not_found: '&7Záloha s tímto názvem neexistuje ve složce backups!'
    save:
      saving: '&7Ukládám zálohu...'
      saved: '&7Zálohována &a{type} &7data jako &a{file}&7'
      error: '&7Chyba při zálohování &a{type}&7 dat! Viz chybu v konzoli/níže pro detaily.'
  claimitems:
    no_items: '&7Nemáš žádné předměty k vyzvednutí.'
  item:
    give:
      sender: '&7Dal jsi &b{amount}x {key} &7hráči &a{player}&7.'
      receiver: '&7Dostal jsi &b{amount}x {key}&7.'
    modifier:
      add:
        added: '&a{stat} &7modifikátor s úrovní &b{value} &7byl přidán k tvému předmětu.'
        already_exists: '&7Modifikátor předmětu se statistikou {color}{stat} &7již na tvém brnění existuje! Nejdříve ho odstraň pomocí &a/sk item modifier remove&7.'
        lore: '&7{stat}: {color}+{value}'
        lore_subtract: '&7{stat}: {color}-{value}'
      remove:
        does_not_exist: '&7Tvůj předmět nemá {color}{stat} &7modifikátor!'
        removed: '&a{stat} &7modifikátor byl odstraněn z tvého předmětu.'
      list:
        header: '&8[&a&l!&8] &7Nalezené modifikátory předmětu:'
        entry: '  &f• &f{stat}: &a{value}'
      removeall:
        removed: '&7Všechny modifikátory předmětu byly odstraněny z tvého předmětu.'
    multiplier:
      add:
        added: '&a{target} &7multiplikátor s hodnotou &b{value} &7byl přidán k tvému předmětu.'
        already_exists: '&7Multiplikátor předmětu s cílem &b{target} &7již na tvém předmětu existuje! Nejdříve ho odstraň pomocí &a/sk item multiplier remove&7.'
        skill_lore: '&7Při držení: &a+{value}% {skill} XP.'
        skill_lore_subtract: '&7Při držení: &a-{value}% {skill} XP.'
        global_lore: '&7Při držení: &9+{value}% Skill XP.'
        global_lore_subtract: '&7Při držení: &9-{value}% Skill XP.'
      remove:
        does_not_exist: '&7Tvůj předmět nemá &b{target} &7multiplikátor předmětu!'
        removed: '&a{target} &7multiplikátor předmětu byl odstraněn z tvého předmětu.'
      list:
        header: '&8[&a&l!&8] &7Nalezené multiplikátory předmětu:'
        entry: '  &f• &f{target}: &a{value}'
      removeall:
        removed: '&7Všechny multiplikátory předmětu byly odstraněny z tvého předmětu.'
    register:
      registered: '&7Registrován držený předmět ke klíči &b{key}'
      already_registered: '&7Již existuje předmět registrovaný ke klíči &f&n{key}&7, použij &a/skills item unregister &7pro jeho zrušení registrace.'
      no_spaces: '&7Klíč předmětu nemůže obsahovat mezery!'
    requirement:
      add:
        added: '&a{skill}&7 požadavek předmětu s úrovní &b{level}&7 byl přidán k tvému předmětu.'
        already_exists: '&7Předmět již má &a{skill}&7 požadavek předmětu! Nejdříve ho odstraň pomocí &a/sk item requirement remove&7.'
        lore: '&7Vyžaduje &a{skill} {level}&7.'
      remove:
        does_not_exist: '&7Tvůj předmět nemá &a{skill}&7 požadavek předmětu!'
        removed: '&a{skill}&7 požadavek předmětu byl odstraněn z tvého předmětu.'
      list:
        header: '&8[&a&l!&8] &7Nalezené požadavky předmětu:'
        entry: '  &f• &f{skill}: &a{level}'
      removeall:
        removed: '&7Všechny požadavky předmětu byly odstraněny z tvého předmětu.'
      use: '&7Nesplňuješ požadavky pro použití tohoto předmětu: {requirements}'
      entry: '&a{skill} {level}&7, '
    unregister:
      unregistered: '&7Zrušena registrace předmětu s klíčem &b{key}&7.'
      not_registered: '&7Neexistuje žádný předmět registrovaný s klíčem &b{key}&7.'
  lang:
    set: '&7Jazyk nastaven na &e{lang}&7.'
    not_found: '&7Jazyk nenalezen!'
  mana:
    display: '&7Tvoje {mana_unit}: &b{current}/{max}&7.'
    display_other: '&7{mana_unit} hráče &a{player}&7: &b{current}/{max}'
    add: '&7Přidáno &b{amount} {mana_unit} &7hráči &a{player}'
    remove: '&7Odebráno &b{amount} {mana_unit} &7hráči &a{player}'
    set: '&7Nastaveno hráči &a{player} &7na &b{amount} {mana_unit}'
    at_least_zero: '&b{mana_unit}&7 musí být alespoň nula!'
    console_specify_player: '&7Konzole musí specifikovat hráče!'
  modifier:
    add:
      added: '{color}{stat} &7modifikátor úroveň &b{value} &7se jménem &f&n{name}&7 přidán hráči &a{player}&7.'
      already_exists: '&7Modifikátor se jménem &f&n{name}&7 již existuje u hráče &a{player}&7! Odstraň ho pomocí &a/sk modifier remove {player} {name}&7.'
    remove:
      not_found: '&7Modifikátor &b{name}&7 nenalezen u hráče &a{player}&7.'
      removed: '&7Modifikátor &b{name} &7odstraněn od hráče &a{player}&7.'
    list:
      all_stats_header: '&8[&a&l!&8] &7Stat modifikátory pro hráče &a{player}:'
      all_stats_entry: '  &f• &f{name}: {color}{stat} &aÚroveň {value}&7.'
      one_stat_header: '&7Stat modifikátory ({color}{stat}&7) pro hráče &a{player}:'
      one_stat_entry: '  &f{name} - {color}{stat} &fÚroveň &6{value}'
      players_only: '&ePoze hráči mohou zobrazit své vlastní modifikátory! Zkus specifikovat hráče'
    removeall:
      removed_all_stats: '&7Odstraněno &b{num} &7stat modifikátorů od hráče &a{player}'
      removed_one_stat: '&7Odstraněno &b{num} {color}{stat} &7modifikátorů od hráče &a{player}'
      players_only: '&ePoze hráči mohou odstranit své vlastní modifikátory! Zkus specifikovat hráče'
  multiplier:
    global: Globální
    list: '&7Globální XP multiplikátor pro &a{player}&7: &b{multiplier}x / +{percent}%'
    skill_entry: '&b{skill} &7XP multiplikátor: &b{multiplier}x / +{percent}%'
    players_only: '&7Pouze hráči mohou zobrazit svůj vlastní multiplikátor! Specifikuj hráče!'
  profile:
    skills: 'Aurelium Skills profil pro &6{name}:\n&7UUID: &f{uuid}\n&7Dovednosti:{skill_entries}'
    skill_entry: '\n  &3{skill} &7- Úroveň: &f{level}&7, XP: &f{xp}'
    stats: 'Aurelium Skills stats profil pro &6{name}:\n&7UUID: &f{uuid}\n&7Statistiky:{stat_entries}'
    stat_entry: '\n  &3{stat} &7- Celková Úroveň: &f{total_level}&7, Základní Úroveň: &f{base_level}&7, Upravená Úroveň: &f{modified_level}'
  rank:
    header: '&8[&a&l!&8] &7Tvé Žebříčky Dovedností'
    power: '&fSíla &7(Všechny Dovednosti): &a#{rank} z {total}'
    entry: '  &f• &f{skill}: &a#{rank} z {total}'
  reload: '&7Config znovu načten!'
  save:
    already_saving: '&7Data se již ukládají!'
    mysql_not_enabled: '&7MySQL nebylo povoleno při startu serveru, ukládám do souboru místo toho!'
    saved: '&7Skill data uložena!'
  skill:
    reset:
      reset_all: '&7Všechny Dovednosti nastaveny na úroveň &b1 &7pro hráče &a{player}'
      reset_skill: '&7Dovednost &b{skill} &7nastavena na úroveň &b1 &7pro hráče &a{player}'
    setall:
      at_least_one: '&eÚroveň musí být alespoň 1!'
      set: '&7Všechny dovednosti nastaveny na úroveň &b{level} &7pro hráče &a{player}'
    setlevel:
      at_least_one: '&eÚroveň musí být alespoň 1!'
      set: '&7Dovednost &b{skill} &7nastavena na úroveň &b{level} &7pro hráče &a{player}'
  toggle:
    enabled: '&7Tvůj skills action bar byl &apovolený&7.'
    disabled: '&7Tvůj skills action bar byl &czakázaný&7.'
    not_enabled: '&7Skills action bar není povolen na tomto serveru!'
  top:
    power_entry: '&f{rank}. &b{player} &f- {level}'
    power_header: '&f--==&b&lŽebříček Všech Dovedností&f==--'
    power_header_page: '&f--==&b&lŽebříček Všech Dovedností &f(Stránka {page})==--'
    skill_header: '&f--==&b&l{skill} Žebříček&f==--'
    skill_header_page: '&f--==&b&l{skill} Žebříček &f(Stránka {page})==--'
    skill_entry: '&f{rank}. &b{player} &f- {level}'
    average_header: '&f--==&b&lŽebříček Průměru Dovedností&f==--'
    average_header_page: '&f--==&b&lŽebříček Průměru Dovedností &f(Stránka {page})==--'
    average_entry: '&f{rank}. &b{player} &f- {level}'
    usage: '&ePoužití: &a/sk top &f<stránka> &7nebo &a/sk top &f[dovednost] <stránka>'
  transfer:
    success: '&7Úspěšně přenesena data hráče z &a{from} &7na &a{to}'
    error: '&7Došlo k chybě při přenášení dat hráče. Zkontroluj konzoli pro chyby.'
  updateleaderboards:
    already_updating: '&7Žebříček se již aktualizuje!'
    updated: '&7Žebříček aktualizován!'
  version: '&7Používáš Aurelium Skills verzi &b{current_version}&7. Nejnovější verze je &b{latest_version}'
  xp:
    add: '&7Přidáno &b{amount} {skill} &7XP hráči &a{player}'
    set: '&7Nastaveno &b{skill} &7XP pro hráče &a{player} &7na &a{amount}'
    remove: '&8[&a&l!&8] &7Odebráno &b{amount} {skill} &7XP hráči &a{player}'
  unknown_skill: '&8[&a&l!&8] &7Neznámá dovednost!'
  no_profile: '&8[&a&l!&8] &7Hráč nemá skills profil. Zkus se znovu připojit do hry nebo restartovat server.'
leveler:
  title: '&b{skill} &lPOSTUP ÚROVNĚ!'
  subtitle: '&7{old} ➜ &3&l{new}'
  level_up: '&7\n&f :skills_box:     &f&lPOSTUP ÚROVNĚ DOVEDNOSTI!\n&7                 &b{skill} &7{old}&7➜&3&l{new}\n&7{stat_level}{ability_unlock}{ability_level_up}{mana_ability_unlock}{mana_ability_level_up}{money_reward}\n&3'
  stat_level: '\n&7                  {color}+{num}{symbol} {stat}'
  ability_unlock: '\n&7                  &f• &fᴀʙɪʟɪᴛʏ ᴏᴅᴇᴍᴋɴᴜᴛᴀ: &a{ability}'
  ability_level_up: '\n&7                  &f• &fᴀʙɪʟɪᴛʏ ᴘᴏsᴛᴜᴘ: &a{ability} &7(Úroveň &f&n{level}&7)'
  mana_ability_unlock: '\n&7                  &f• &bᴍᴀɴᴀ ᴀʙɪʟɪᴛʏ ᴏᴅᴇᴍᴋɴᴜᴛᴀ: &9{mana_ability}'
  mana_ability_level_up: '\n&7                  &f• &bᴍᴀɴᴀ ᴀʙɪʟɪᴛʏ ᴘᴏsᴛᴜᴘ: &9{mana_ability} &7(Úroveň &f&n{level}&7)'
  money_reward: '\n&7                  &f• &fᴘᴇɴɪᴢᴇ: &a${amount}'
  unclaimed_item: '&8[&a&l!&8] &7Máš nevyzvednuté předměty, protože tvůj inventář byl plný, použij &a/skills claimitems &7pro jejich vyzvednutí'
menus:
  common:
    close: '&c&lZavřít'
    level: '&aInformace:\n  &f• &fᴜʀᴏᴠᴇɴ: &a{level}'
    progress_to_level: '&aPokrok:\n&f  • &fᴅᴀʟsɪ ᴜʀᴏᴠᴇɴ:&a {level}\n  &f• &fsᴏᴜᴄᴀsɴᴇ xᴘ: &d{current_xp}/{level_xp} {xp_unit} &8({percent}%)'
    max_level: '&a&l✔ &fDosáhl jsi &a&lᴍᴀxɪᴍᴀʟɴɪ&f úroveň!'
    ability_levels: '\n&aÚrovně Schopností:\n{ability_1}\n{ability_2}\n{ability_3}\n{ability_4}\n{ability_5}\n '
    ability_level_entry: ' &a✔ &f{ability} {level} &8({info})'
    ability_level_entry_locked: ' &8- &m{ability}'
    stats_leveled: '\n&aVylepšené Statistiky:\n  &f• &f{stats}'
    mana_ability: '\n&aMana Schopnost: &9{mana_ability} {level}\n  &7Trvání: &a{duration}s &7Cena Many: &b{mana_cost} &7Cooldown: &e{cooldown}s\n '
  skills_menu:
    skills_menu_title: Tvé Dovednosti
    your_skills: '&bTvé Dovednosti - &a{player}'
    your_skills_desc: '&7Vylepšuj Dovednosti prováděním různých úkolů\n&7pro odemknutí cenných stat bonusů,\n&7schopností a dalšího!'
    your_skills_hover: '&eNajeď myší na Dovednost pro více informací!'
    your_skills_click: '&eKlikni na Dovednost pro zobrazení postupu úrovní!'
    skill_click: '&eKlikni pro zobrazení Postupu Úrovní!'
    skill_locked: '&cNemáš oprávnění k zobrazení této dovednosti!'
    stats: '&bStatistiky'
    stats_desc: '&7Získávej stat bonusy vylepšováním dovedností'
    stats_click: '&eKlikni pro více informací nebo zobrazení tvých statistik'
  level_progression_menu:
    level_progression_menu_title: '&f:offset_-8::skills_progression:'
    your_ranking: '&a&lTvé Žebříčky Dovedností'
    rank_out_of: '&8ɪɴᴛᴇʀᴀᴋᴛɪᴠɴɪ\n&7\n&7Jsi na &a{rank}&7 místě z &a{total}&7 hráčů.'
    rank_percent: '&7Jsi v top &c{percent}%&7 hráčů.'
    back: '&c&lZpět'
    back_click: '&8ɪɴᴛᴇʀᴀᴋᴛɪᴠɴɪ'
    next_page: '&a&lDalší Stránka'
    next_page_click: '&8ɪɴᴛᴇʀᴀᴋᴛɪᴠɴɪ'
    previous_page: '&a&lPředchozí Stránka'
    previous_page_click: '&8ɪɴᴛᴇʀᴀᴋᴛɪᴠɴɪ'
    level_unlocked: '&a&lÚroveň {level}'
    level_in_progress: '&e&lÚroveň {level}'
    level_locked: '&c&lÚroveň {level}'
    level_number: '&8ᴜʀᴏᴠᴇɴ {level}\n&7\n'
    unlocked: '&a&l✔ ᴜʀᴏᴠᴇɴ &fodemknuta.'
    in_progress: '&e♻ &fÚroveň &e&lᴠ ᴘʀᴏɢʀᴇsᴜ&f.'
    locked: '&c&l✖ &fÚroveň &c&lɴᴇɴɪ &fdosažena.'
    rewards: '&aOdměny:{rewards}'
    rewards_entry: '\n {color}+{num} {symbol} {stat}'
    money_reward: '\n &a${amount} &fᴘᴇɴᴇᴢ'
    ability_unlock: '\n&aVybavení Schopností:\n  &f• &f{ability} &8▏ &a&lᴏᴅᴇᴍᴋɴᴜᴛɪ sᴄʜᴏᴘɴᴏsᴛɪ\n   &7{desc}\n '
    ability_level: '\n&aVybavení Schopností:\n  &f• &f{ability} &7(Úroveň {level})\n   &7{desc}\n '
    mana_ability_unlock: '\n  &f• &9{mana_ability} &8▏ &b&lᴏᴅᴇᴍᴋɴᴜᴛɪ ᴍᴀɴᴀ sᴄʜᴏᴘɴᴏsᴛɪ\n   &7{desc}\n '
    mana_ability_level: '\n  &f• &9{mana_ability} &7(Úroveň {level})\n   &7{desc}\n '
    progress: '&aInformace:\n  &f• &fᴘᴏᴋʀᴏᴋ: &d&7{current_xp}/{level_xp} {xp_unit} &8({percent}%)'
    leaderboard_click: '&a▶ &lᴋʟɪᴋɴɪ &fpro zobrazení žebříčku.'
    sources: '&a&lZdroje XP'
    sources_desc: '&8ɪɴᴛᴇʀᴀᴋᴛɪᴠɴɪ\n&7\n&7Menu &f&nzdrojů&7 ukazuje všechny způsoby,\n&7jak můžeš získávat &d&lxᴘ&7 v dovednosti, stejně jako\n&7kolik &d&lxᴘ&7 každý zdroj dává.'
    sources_click: '&a▶ &lᴋʟɪᴋɴɪ &fpro zobrazení.'
    abilities: '&b&lSchopnosti'
    abilities_desc: '&8ɪɴᴛᴇʀᴀᴋᴛɪᴠɴɪ\n&7\n&7Schopnosti jsou &f&npasivní&7 výhody, které\n&a&lᴏᴅᴇᴍʏᴋᴀs &7a &d&lᴠʏʟᴇᴘsᴜᴊᴇs&7 jak\n&7vylepšuješ dovednosti.\n&7\n&bMana Schopnosti &7jsou speciální typ\n&7schopnosti, který vyžaduje aktivaci\n&7a spotřebovává &b&lᴍᴀɴᴜ&7.'
    abilities_click: '&a▶ &lᴋʟɪᴋɴɪ &fpro zobrazení.'
  stats_menu:
    stats_menu_title: Tvé Statistiky
    player_stat_entry: ' {color}{symbol} {stat} &f{level}'
    skills: '\n&7Dovednosti: &f{skills}'
    your_level: '&7Tvá Úroveň: {color}{level}'
    attack_damage: '&4+{value}% Útočné Poškození'
    hp: '&c+{value} ❤'
    saturated_regen: '&a+{value} Nasycená Regenerace'
    full_hunger_regen: '&a+{value} Regenerace při Plném Hladu'
    almost_full_hunger_regen: '&a+{value} Regenerace při Skoro Plném Hladu'
    mana_regen: '&b+{value} Mana/s Regenerace'
    luck: '&2+{value} Štěstí'
    double_drop_chance: '&2Šance Dvojnásobného Dropu: {value}%'
    xp_gain: '&9+{value}% {xp_unit} Zisk'
    anvil_cost_reduction: '&9Snížení Ceny Kovadliny: {value}%'
    max_mana: '&9Max Mana: {value}'
    incoming_damage: '&5-{value}% Příchozí Poškození'
  unclaimed_items:
    unclaimed_items_title: 'Vyzvedni Předměty'
    inventory_full: '&eNemáš dostatek místa v inventáři pro vyzvednutí tohoto předmětu'
    click_to_claim: '&eKlikni pro vyzvednutí!'
  leaderboard:
    leaderboard_title: '&f:offset_-8::skills_leaderboard:'
    player_entry: '&a{place}. &f{player}'
    skill_level: '&7ᴜʀᴏᴠᴇɴ: {level}'
  sources:
    sources_title: '&f:offset_-8::skills_xpsources:'
    sorter: '&a&lSeřadit Zdroje XP'
    sort_type: '&7{selected}{type_name}\n'
    selected: '&a▶ '
    descending: 'Nejvyšší k Nejnižším'
    ascending: 'Nejnižší k Nejvyšším'
    alphabetical: 'Název (A-Z)'
    reverse_alphabetical: 'Název (Z-A)'
    sort_click: '&a▶ &lᴋʟɪᴋɴɪ &fpro seřazení.'
    source_name: '&f{name}'
    source_xp: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&aInformace:\n  &f• &fxᴘ: &d{xp}'
    source_xp_rate: '  &f• &fxᴘ: &d{xp}/{unit}'
    multiplied_xp: '\n  &f• &fᴠʏɴᴀsᴏʙᴇɴᴇ xᴘ: &d{xp}'
    multiplied_xp_rate: '\n  &f• &fᴠʏɴᴀsᴏʙᴇɴᴇ xᴘ: &d{xp}/{unit}'
    multiplied_desc: '\n &8ℹ Toto jsou XP, které skutečně získáš\n  &8   na základě tvých současných XP multiplikátorů'
  abilities:
    abilities_title: '&f:offset_-8::skills_abilities:'
    locked_desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&aPopis:\n  &7{desc}'
    unlocked_at: '&7Odemknuto na &b&l{skill} {level}'
    your_ability_level: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Tvá &f&nsouasna&7 úroveň je &b&l{level}&7!'
    your_ability_level_maxed: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Tvá &f&nsouasna&7 úroveň je &b&l{level}&7! &c(MAX!)'
    unlocked_desc: '&fDalší vylepšení na &3{skill} {level}&f\n  &7{desc}'
    unlocked_desc_maxed: '&aPopis:\n  &7{desc}'
    desc_upgrade_value: '&8{current}→&a{next}&7'
mana_abilities:
  replenish:
    name: 'ᴅᴏᴘʟɴᴇɴɪ'
    desc: '&7Znovu zasadí plodiny &f&nautomaticky&7 po \n&7   {value} sekund. &e[Pravý klik motyka a\n&e   zlom plodinu pro aktivaci]'
    raise: '&7Zvedáš svou motyku'
    lower: '&7Spouštíš svou motyku'
    start: '&aDoplnění Aktivováno! &7(-{mana} {mana_unit})'
    end: '&7Doplnění pominulo'
  treecapitator:
    name: 'ᴋᴀᴄᴇɴɪ sᴛʀᴏᴍᴜ'
    desc: '&7Láme &acelé&7 stromy okamžitě po \n&b   {value} sekund&7. &e[Pravý klik sekera a\n&e   zlom kmen pro aktivaci]'
    raise: '&7Zvedáš svou sekeru'
    lower: '&7Spouštíš svou sekeru'
    start: '&aKácení Stromu Aktivováno! &7(-{mana} {mana_unit})'
    end: '&7Kácení Stromu pominulo'
  speed_mine:
    name: 'ʀʏᴄʜʟᴇ ᴛᴇᴢᴇɴɪ'
    desc: '&7Dává &eHaste {haste_level}&7 na {value} sekund.\n&e   [Pravý klik krumpáč a těž\n&e   blok pro aktivaci]'
    raise: '&7Zvedáš svůj krumpáč'
    lower: '&7Spouštíš svůj krumpáč'
    start: '&aRychlé Těžení Aktivováno! &7(-{mana} {mana_unit})'
    end: '&7Rychlé Těžení pominulo'
  absorption:
    name: 'ᴀʙsᴏʀᴘᴄᴇ'
    desc: 'Příchozí poškození sníží &bmanu\n&7   o &a2x&7 Minecraft poškození místo tvého\n&7   zdraví po {value} sekund. Mana se nebude\n&7   regenerovat, dokud je Absorpce aktivní.\n   &e[Levý klik štít a vezmi poškození pro aktivaci]'
    raise: '&7Zvedáš svůj štít'
    lower: '&7Spouštíš svůj štít'
    start: '&aAbsorpce Aktivována! &7(-{mana} {mana_unit})'
    end: '&7Absorpce pominula'
  terraform:
    name: 'ᴛᴇʀᴀғᴏʀᴍᴏᴠᴀɴɪ'
    desc: 'Láme bloky okamžitě v okruhu 4 bloků\n&7   ve stejné vrstvě při kopání po {value} sekund. Musíš\n&7   použít lopatu a extra zlomené bloky musí být\n&7   stejného typu a v jedné spojené žíle.\n&e   [Pravý klik lopata a kopej blok pro aktivaci]'
    raise: '&7Zvedáš svou lopatu'
    lower: '&7Spouštíš svou lopatu'
    start: '&aTerraformování Aktivováno! &7(-{mana} {mana_unit})'
    end: '&7Terraformování pominulo'
  sharp_hook:
    name: 'ᴏsᴛʀʏ ʜᴀᴄᴇᴋ'
    desc: 'Způsob &c{value}&7 poškození zachycené entitě\n&7   při levém kliku s rybářským prutem.'
    use: '&aPoužit Ostrý Háček! &7(-{mana} {mana_unit})'
    menu: '\n&d&lMana Schopnost &9{mana_ability} {level}\n  &7Poškození: &c{value} &7Cena Many: &b{mana_cost} &7Cooldown: &e{cooldown}s\n '
  charged_shot:
    name: 'ɴᴀʙɪᴛʏ ᴠʏsᴛʀᴇʟ'
    desc: 'Šípy, které vystřelíš, způsobí více poškození\n&7   na základě toho, jak daleko byl luk natažen,\n&7   spotřebovávající &bmanu&7 v procesu. Způsobuje &a{value}%&7 více\n&7   poškození za spotřebovanou manu. &e[Levý klik\n&e   luk pro přepnutí režimu nabitého výstřelu]'
    enable: '&7Režim nabitého výstřelu je nyní povolen'
    disable: '&7Režim nabitého výstřelu je nyní zakázán'
    shoot: '&aPoužit Nabitý Výstřel! &d+{percent}% POŠKOZENÍ &7(-{mana} {mana_unit})'
    menu: '\n&d&lMana Schopnost &9{mana_ability} {level}\n  &7Poškození: &c+{value}% / Mana &7Max Cena Many: &b{mana_cost}\n '
  lightning_blade:
    name: 'ʙʟᴇsᴋᴏᴠᴀ ᴄᴇᴘᴇʟ'
    desc: 'Zvyšuje rychlost útoku o &a{value}%&7 na {duration} sekund.\n&e   [Pravý klik meč a zaútoč na moba pro aktivaci]'
    raise: '&7Připravuješ svůj meč'
    lower: '&7Spouštíš svůj meč'
    start: '&aBlesková Čepel Aktivována! &7(-{mana} {mana_unit})'
    end: '&7Blesková Čepel pominula'
    menu: '\n&d&lMana Schopnost &9{mana_ability} {level}\n  &7Rychlost Útoku: &c+{value}% &7Trvání: &a{duration}s\n  &7Cena Many: &b{mana_cost} &7Cooldown: &e{cooldown}s\n '
  not_ready: '&cSchopnost není připravena! &7({cooldown}s)'
  not_enough_mana: '&eNedostatek many! &7({mana} {mana_unit} potřeba, máš {current_mana}/{max_mana})'
rewards:
  item:
    default_menu_message: '\n  {display_name}'
    default_menu_message_multiple: '\n  {display_name} {amount}x'
    default_chat_message: '\n  {display_name}'
    default_chat_message_multiple: '\n  {display_name} {amount}x'
skills:
  farming:
    name: Farmářství
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Sklízej plodiny pro &f&nzískání&7 Farmářských {xp_unit}.'
  foraging:
    name: Sběračství
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Kácej stromy pro &f&nzískání&7 Sběračských {xp_unit}.'
  mining:
    name: Hornictví
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Těž kámen a rudy pro &f&nzískání&7 Hornických {xp_unit}.'
  fishing:
    name: Rybaření
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Chytej ryby pro &f&nzískání&7 Rybářských {xp_unit}.'
  excavation:
    name: Vykopávky
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Kopej lopatou pro &f&nzískání&7 Vykopávkových {xp_unit}.'
  archery:
    name: Lukostřelba
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Střílej na moby a hráče lukem\n&7pro &f&nzískání&7 Lukostřeleckých {xp_unit}.'
  defense:
    name: Obrana
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Přijímej poškození od entit\n&7pro &f&nzískání&7 Obranných {xp_unit}.'
  fighting:
    name: Boj
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Bojuj s moby pomocí zbraní na blízko\n&7pro &f&nzískání&7 Bojových {xp_unit}.'
  endurance:
    name: Vytrvalost
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Choď a běhej pro &f&nzískání&7 Vytrvalostních {xp_unit}.'
  agility:
    name: Obratnost
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Skákej a přijímej poškození z pádu\n&7pro &f&nzískání&7 Obratnostních {xp_unit}.'
  alchemy:
    name: Alchymie
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Vař lektvary pro &f&nzískání&7 Alchymistických {xp_unit}.'
  enchanting:
    name: Enchantování
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Enchantuj předměty a knihy\n&7pro &f&nzískání&7 Enchantovacích {xp_unit}.'
  sorcery:
    name: Čarodějnictví
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Používej &bmana schopnosti&7 pro\n&f&nzískání&7 Čarodějnických {xp_unit}.'
  healing:
    name: Léčení
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Pij a rozstřikuj lektvary\n&7pro &f&nzískání&7 Léčitelských {xp_unit}.'
  forging:
    name: Kování
    desc: '&7Kombinuj a aplikuj knihy v\n&7kovadlině pro &f&nzískání&7 Kovářských {xp_unit}.'
sources:
  farming:
    wheat: Pšenice
    potato: Brambora
    carrot: Mrkev
    beetroot: Červená Řepa
    nether_wart: Netherová Bradavice
    pumpkin: Dýně
    melon: Meloun
    sugar_cane: Cukrová Třtina
    bamboo: Bambus
    cocoa: Kakao
    cactus: Kaktus
    brown_mushroom: Hnědá Houba
    red_mushroom: Červená Houba
    kelp: Řasa
    sea_pickle: Mořské Okurky
    sweet_berry_bush: Sladké Bobule
    glow_berries: Světlušky
    torchflower: Pochodňový Květ
    pitcher_plant: Džbánková Rostlina
  foraging:
    oak_log: Dubové Poleno
    spruce_log: Smrkové Poleno
    birch_log: Březové Poleno
    jungle_log: Džunglové Poleno
    acacia_log: Akáciové Poleno
    dark_oak_log: Tmavě Dubové Poleno
    oak_leaves: Dubové Listí
    spruce_leaves: Smrkové Listí
    birch_leaves: Březové Listí
    jungle_leaves: Džunglové Listí
    acacia_leaves: Akáciové Listí
    dark_oak_leaves: Tmavě Dubové Listí
    crimson_stem: Karmínový Kmen
    warped_stem: Pokřivený Kmen
    nether_wart_block: Blok Netherové Bradavice
    warped_wart_block: Blok Pokřivené Bradavice
    moss_block: Mechový Blok
    moss_carpet: Mechový Koberec
    azalea: Azalka
    flowering_azalea: Kvetoucí Azalka
    azalea_leaves: Azalkové Listí
    flowering_azalea_leaves: Kvetoucí Azalkové Listí
    mangrove_log: Mangrovové Poleno
    mangrove_leaves: Mangrovové Listí
    mangrove_roots: Mangrovové Kořeny
    cherry_log: Třešňové Poleno
    cherry_leaves: Třešňové Listí
    pink_petals: Růžové Okvětní Lístky
  mining:
    stone: Kámen
    cobblestone: Dlažební Kámen
    andesite: Andezit
    diorite: Diorit
    granite: Žula
    coal_ore: Uhelná Ruda
    nether_quartz_ore: Netherová Křemenná Ruda
    iron_ore: Železná Ruda
    lapis_ore: Lazuritová Ruda
    redstone_ore: Redstoneová Ruda
    gold_ore: Zlatá Ruda
    diamond_ore: Diamantová Ruda
    emerald_ore: Smaragdová Ruda
    terracotta: Terakota
    white_terracotta: Bílá Terakota
    orange_terracotta: Oranžová Terakota
    yellow_terracotta: Žlutá Terakota
    light_gray_terracotta: Světle Šedá Terakota
    brown_terracotta: Hnědá Terakota
    red_terracotta: Červená Terakota
    netherrack: Netherrack
    blackstone: Černý Kámen
    basalt: Čedič
    magma_block: Magmový Blok
    nether_gold_ore: Netherová Zlatá Ruda
    ancient_debris: Prastaré Trosky
    end_stone: Endový Kámen
    obsidian: Obsidián
    deepslate: Břidlice
    copper_ore: Měděná Ruda
    tuff: Tuf
    calcite: Kalcit
    smooth_basalt: Hladký Čedič
    amethyst_block: Ametystový Blok
    amethyst_cluster: Ametystový Shluk
    deepslate_coal_ore: Břidlicová Uhelná Ruda
    deepslate_iron_ore: Břidlicová Železná Ruda
    deepslate_copper_ore: Břidlicová Měděná Ruda
    deepslate_gold_ore: Břidlicová Zlatá Ruda
    deepslate_redstone_ore: Břidlicová Redstoneová Ruda
    deepslate_lapis_ore: Břidlicová Lazuritová Ruda
    deepslate_emerald_ore: Břidlicová Smaragdová Ruda
    deepslate_diamond_ore: Břidlicová Diamantová Ruda
    dripstone_block: Blok Krápníku
    ice: Led
    packed_ice: Pevný Led
    blue_ice: Modrý Led
    reinforced_deepslate: Zesílená Břidlice
  fishing:
    cod: Treska
    salmon: Losos
    tropical_fish: Tropická Ryba
    pufferfish: Ježovka
    treasure: Pokladové Předměty
    junk: Odpadkové Předměty
    rare: Vzácný Loot
    epic: Epický Loot
  excavation:
    dirt: Hlína
    grass_block: Travnatý Blok
    sand: Písek
    gravel: Štěrk
    mycelium: Mycelium
    clay: Jíl
    soul_sand: Písek Duší
    coarse_dirt: Hrubá Hlína
    podzol: Podzol
    soul_soil: Půda Duší
    red_sand: Červený Písek
    rooted_dirt: Zakořeněná Hlína
    mud: Bahno
    muddy_mangrove_roots: Bahnitý Mangrovový Kořen
  mobs:
    player: Hráč
    bat: Netopýr
    cat: Kočka
    chicken: Slepice
    cod: Treska
    cow: Kráva
    donkey: Osel
    fox: Liška
    giant: Obr
    horse: Kůň
    mooshroom: Houbová Kráva
    mule: Mezek
    ocelot: Ocelot
    parrot: Papoušek
    pig: Prase
    rabbit: Králík
    salmon: Losos
    sheep: Ovce
    skeleton_horse: Kostlivý Kůň
    snow_golem: Sněhulák
    squid: Chobotnice
    strider: Strider
    tropical_fish: Tropická Ryba
    turtle: Želva
    villager: Vesničan
    wandering_trader: Putující Obchodník
    bee: Včela
    cave_spider: Jeskynní Pavouk
    dolphin: Delfín
    enderman: Enderman
    iron_golem: Železný Golem
    llama: Lama
    piglin: Piglin
    panda: Panda
    polar_bear: Lední Medvěd
    pufferfish: Ježovka
    spider: Pavouk
    wolf: Vlk
    zombified_piglin: Zombifikovaný Piglin
    blaze: Blaze
    creeper: Creeper
    drowned: Utopený
    elder_guardian: Starší Strážce
    endermite: Endermite
    evoker: Vyvolávač
    ghast: Ghast
    guardian: Strážce
    hoglin: Hoglin
    husk: Mumie
    illusioner: Iluzionista
    magma_cube: Magmová Kostka
    phantom: Fantom
    piglin_brute: Piglin Brutál
    pillager: Loupežník
    ravager: Ničitel
    shulker: Shulker
    silverfish: Rybenka
    skeleton: Kostlivec
    slime: Sliz
    stray: Tulák
    vex: Vex
    vindicator: Mstitel
    witch: Čarodějnice
    wither_skeleton: Wither Kostlivec
    zoglin: Zoglin
    zombie: Zombie
    zombie_villager: Zombie Vesničan
    ender_dragon: Ender Drak
    wither: Wither
    axolotl: Axolotl
    glow_squid: Světlušková Chobotnice
    goat: Koza
    allay: Allay
    frog: Žába
    tadpole: Pulec
    warden: Strážce
    camel: Velbloud
    sniffer: Čmuchač
  defense:
    mob_damage: Poškození od Mobů
    player_damage: Poškození od Hráčů
  endurance:
    walk_per_meter: Chůze
    sprint_per_meter: Běh
    swim_per_meter: Plavání
  agility:
    jump_per_100: Skákání
    fall_damage: Přijímání Poškození z Pádu
  alchemy:
    awkward: Neobratný Lektvar
    regular: Běžný Lektvar
    extended: Lektvar s Prodlouženým Trváním
    upgraded: Vylepšený Lektvar
    splash: Rozstřikovací Lektvar
    lingering: Přetrvávající Lektvar
  enchanting:
    weapon_per_level: Zbraň
    armor_per_level: Brnění
    tool_per_level: Nástroj
    book_per_level: Kniha
  sorcery:
    mana_ability_use: Používání Mana Schopností
    sculk: Sculk
    sculk_vein: Sculk Žíla
    sculk_sensor: Sculk Senzor
    sculk_catalyst: Sculk Katalyzátor
    sculk_shrieker: Sculk Křičoun
  healing:
    drink_regular: Pití Běžného Lektvaru
    drink_extended: Pití Lektvaru s Prodlouženým Trváním
    drink_upgraded: Pití Vylepšeného Lektvaru
    splash_regular: Rozstříknutí Běžného Lektvaru
    splash_extended: Rozstříknutí Lektvaru s Prodlouženým Trváním
    splash_upgraded: Rozstříknutí Vylepšeného Lektvaru
    lingering_regular: Použití Běžného Přetrvávajícího Lektvaru
    lingering_extended: Použití Přetrvávajícího Lektvaru s Prodlouženým Trváním
    lingering_upgraded: Použití Vylepšeného Přetrvávajícího Lektvaru
    golden_apple: Zlaté Jablko
    enchanted_golden_apple: Enchantované Zlaté Jablko
  forging:
    combine_books_per_level: Kombinování Enchantovaných Knih
    combine_weapon_per_level: Kombinování Zbraně s Knihou
    combine_armor_per_level: Kombinování Brnění s Knihou
    combine_tool_per_level: Kombinování Nástroje s Knihou
    grindstone_per_level: Disenchantování Předmětu Brousícím Kamenem
  units:
    damage: Poškození
    meter: Metr
    100_jumps: 100 Skoků
    mana: Spotřebovaná Mana
    combine_level: Kovadlina Cena XP
    grindstone_level: Úroveň Enchantu na Předmětu
    enchant_level: Úroveň Přijatého Enchantu
stats:
  strength:
    name: '&fsɪʟᴀ'
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠɴɪ\n&7\n&7Síla zvyšuje tvé útočné\n&cpoškození &7s různými\n&7zbraněmi'
    color: '&4'
    symbol: '➽'
  health:
    name: '&fᴢᴅʀᴀᴠɪ'
    desc: '&7Zdraví zvyšuje množství {hp_unit}, které\n&7máš, což ti umožňuje vydržet déle\n&7v bojích'
    color: '&c'
    symbol: '❤'
  regeneration:
    name: '&fʀᴇɢᴇɴᴇʀᴀᴄᴇ'
    desc: '&7Regenerace zvyšuje rychlost, kterou\n&7si obnovuješ zdraví i manu'
    color: '&a'
    symbol: '❥'
  luck:
    name: '&fsᴛᴇsᴛɪ'
    desc: '&7Štěstí zvyšuje tvé šance na získání\n&7vzácného lootu od mobů, rybaření a dalšího'
    color: '&2'
    symbol: '☘'
  wisdom:
    name: '&fᴍᴏᴜᴅʀᴏsᴛ'
    desc: '&7Moudrost zvyšuje tvou maximální manu, zvyšuje\n&7získávání zkušeností a snižuje ceny kovadliny'
    color: '&9'
    symbol: '✿'
  toughness:
    name: '&fᴏᴅᴏʟɴᴏsᴛ'
    desc: '&7Odolnost zvyšuje množství\n&7poškození sníženého od nepřátelských útoků'
    color: '&5'
    symbol: '✦'
units:
  mana: Mana
  hp: HP
  xp: XP
file_version: 30

# 184C9YDJB87VI9POW
