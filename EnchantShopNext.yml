open_command:
  - eshop2
  - enchantshop2
  - es2
open_commands:
  - '[sound] BLOCK_COMPARATOR_CLICK'
register_command: true
size: 53

menu_title: '&fऱऩलम'

items:
  'Simple Lucky Gem':
    material: PAPER
    model_data: 3
    slot: 10
    display_name: "&fय Lucky Gem"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 20000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 20000'
      - '[console] ae giveitem %player_name% magic 1 SIMPLE'
      - "[message] &fए&#00d52a You just bought &fय Lucky Gem"
    lore:
      - ''
      - '&#dff9fbApply  to an  enchantment'
      - '&#dff9fbbook to increase the rate'
      - '&#dff9fbof success by &fRandom%'
      - ''
      - '&a+ &fɢᴇᴍ ʀᴀʀɪᴛʏ: &fऄ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 20.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Unique Lucky Gem':
    material: PAPER
    model_data: 3
    slot: 11
    display_name: "&fय Lucky Gem"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 20000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 20000'
      - '[console] ae giveitem %player_name% magic 1 UNIQUE'
      - "[message] &fए&#00d52a You just bought &fय Lucky Gem"
    lore:
      - ''
      - '&#dff9fbApply  to an  enchantment'
      - '&#dff9fbbook to increase the rate'
      - '&#dff9fbof success by &fRandom%'
      - ''
      - '&a+ &fɢᴇᴍ ʀᴀʀɪᴛʏ: &fअ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 20.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Elite Lucky Gem':
    material: PAPER
    model_data: 3
    slot: 12
    display_name: "&fय Lucky Gem"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 20000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 20000'
      - '[console] ae giveitem %player_name% magic 1 ELITE'
      - "[message] &fए&#00d52a You just bought &fय Lucky Gem"
    lore:
      - ''
      - '&#dff9fbApply  to an  enchantment'
      - '&#dff9fbbook to increase the rate'
      - '&#dff9fbof success by &fRandom%'
      - ''
      - '&a+ &fɢᴇᴍ ʀᴀʀɪᴛʏ: &fआ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 20.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Ultimate Lucky Gem':
    material: PAPER
    model_data: 3
    slot: 13
    display_name: "&fय Lucky Gem"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 20000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 20000'
      - '[console] ae giveitem %player_name% magic 1 ULTIMATE'
      - "[message] &fए&#00d52a You just bought &fय Lucky Gem"
    lore:
      - ''
      - '&#dff9fbApply  to an  enchantment'
      - '&#dff9fbbook to increase the rate'
      - '&#dff9fbof success by &fRandom%'
      - ''
      - '&a+ &fɢᴇᴍ ʀᴀʀɪᴛʏ: &fइ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 20.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'


  'Legendary Lucky Gem':
    material: PAPER
    model_data: 3
    slot: 14
    display_name: "&fय Lucky Gem"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 20000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 20000'
      - '[console] ae giveitem %player_name% magic 1 LEGENDARY'
      - "[message] &fए&#00d52a You just bought &fय Lucky Gem"
    lore:
      - ''
      - '&#dff9fbApply  to an  enchantment'
      - '&#dff9fbbook to increase the rate'
      - '&#dff9fbof success by &fRandom%'
      - ''
      - '&a+ &fɢᴇᴍ ʀᴀʀɪᴛʏ: &fई'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 20.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'


  'Fabled Lucky Gem':
    material: PAPER
    model_data: 3
    slot: 15
    display_name: "&fय Lucky Gem"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 20000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 20000'
      - '[console] ae giveitem %player_name% magic 1 FABLED'
      - "[message] &fए&#00d52a You just bought &fय Lucky Gem"
    lore:
      - ''
      - '&#dff9fbApply  to an  enchantment'
      - '&#dff9fbbook to increase the rate'
      - '&#dff9fbof success by &fRandom%'
      - ''
      - '&a+ &fɢᴇᴍ ʀᴀʀɪᴛʏ: &fउ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 20.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Lethal Lucky Gem':
    material: PAPER
    model_data: 3
    slot: 16
    display_name: "&fय Lucky Gem"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 20000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 20000'
      - '[console] ae giveitem %player_name% magic 1 LETHAL'
      - "[message] &fए&#00d52a You just bought &fय Lucky Gem"
    lore:
      - ''
      - '&#dff9fbApply  to an  enchantment'
      - '&#dff9fbbook to increase the rate'
      - '&#dff9fbof success by &fRandom%'
      - ''
      - '&a+ &fɢᴇᴍ ʀᴀʀɪᴛʏ: &fऊ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 20.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Simple Randomization Gem':
    material: PAPER
    model_data: 5
    slot: 19
    display_name: "&fय Randomization Gem"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 60000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 60000'
      - '[console] ae giveitem %player_name% randomizer 1 SIMPLE'
      - "[message] &fए&#00d52a You just bought &fय Randomization Gem"
    lore:
      - ''
      - '&#ffdbdbApply to any enchantment Book'
      - '&#ffdbdbto reroll the success rate and'
      - '&#ffdbdbdestroy rate.'
      - ''
      - '&a+ &fɢᴇᴍ ʀᴀʀɪᴛʏ: &fऄ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 60.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Unique Randomization Gem':
    material: PAPER
    model_data: 5
    slot: 20
    display_name: "&fय Randomization Gem"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 60000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 60000'
      - '[console] ae giveitem %player_name% randomizer 1 UNIQUE'
      - "[message] &fए&#00d52a You just bought &fय Randomization Gem"
    lore:
      - ''
      - '&#ffdbdbApply to any enchantment Book'
      - '&#ffdbdbto reroll the success rate and'
      - '&#ffdbdbdestroy rate.'
      - ''
      - '&a+ &fɢᴇᴍ ʀᴀʀɪᴛʏ: &fअ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 60.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Elite Randomization Gem':
    material: PAPER
    model_data: 5
    slot: 21
    display_name: "&fय Randomization Gem"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 60000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 60000'
      - '[console] ae giveitem %player_name% randomizer 1 ELITE'
      - "[message] &fए&#00d52a You just bought &fय Randomization Gem"
    lore:
      - ''
      - '&#ffdbdbApply to any enchantment Book'
      - '&#ffdbdbto reroll the success rate and'
      - '&#ffdbdbdestroy rate.'
      - ''
      - '&a+ &fɢᴇᴍ ʀᴀʀɪᴛʏ: &fआ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 60.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Ultimate Randomization Gem':
    material: PAPER
    model_data: 5
    slot: 22
    display_name: "&fय Randomization Gem"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 60000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 60000'
      - '[console] ae giveitem %player_name% randomizer 1 ULTIMATE'
      - "[message] &fए&#00d52a You just bought &fय Randomization Gem"
    lore:
      - ''
      - '&#ffdbdbApply to any enchantment Book'
      - '&#ffdbdbto reroll the success rate and'
      - '&#ffdbdbdestroy rate.'
      - ''
      - '&a+ &fɢᴇᴍ ʀᴀʀɪᴛʏ: &fइ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 60.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'


  'Legendary Randomization Gem':
    material: PAPER
    model_data: 5
    slot: 23
    display_name: "&fय Randomization Gem"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 60000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 60000'
      - '[console] ae giveitem %player_name% randomizer 1 LEGENDARY'
      - "[message] &fए&#00d52a You just bought &fय Randomization Gem"
    lore:
      - ''
      - '&#ffdbdbApply to any enchantment Book'
      - '&#ffdbdbto reroll the success rate and'
      - '&#ffdbdbdestroy rate.'
      - ''
      - '&a+ &fɢᴇᴍ ʀᴀʀɪᴛʏ: &fई'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 60.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'


  'Fabled Randomization Gem':
    material: PAPER
    model_data: 5
    slot: 24
    display_name: "&fय Randomization Gem"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 60000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 60000'
      - '[console] ae giveitem %player_name% randomizer 1 FABLED'
      - "[message] &fए&#00d52a You just bought &fय Randomization Gem"
    lore:
      - ''
      - '&#ffdbdbApply to any enchantment Book'
      - '&#ffdbdbto reroll the success rate and'
      - '&#ffdbdbdestroy rate.'
      - ''
      - '&a+ &fɢᴇᴍ ʀᴀʀɪᴛʏ: &fउ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 60.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Lethal Randomization Gem':
    material: PAPER
    model_data: 5
    slot: 25
    display_name: "&fय Randomization Gem"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 60000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 60000'
      - '[console] ae giveitem %player_name% randomizer 1 LETHAL'
      - "[message] &fए&#00d52a You just bought &fय Randomization Gem"
    lore:
      - ''
      - '&#ffdbdbApply to any enchantment Book'
      - '&#ffdbdbto reroll the success rate and'
      - '&#ffdbdbdestroy rate.'
      - ''
      - '&a+ &fɢᴇᴍ ʀᴀʀɪᴛʏ: &fऊ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 60.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'



  'Simple Slots Scroll':
    material: PAPER
    model_data: 14
    slot: 28
    display_name: "&fय Slots Scroll"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 18000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 18000'
      - '[console] ae giveitem %player_name% slotincreaser 1 SIMPLE'
      - "[message] &fए&#00d52a You just bought &fय Slots Scroll"
    lore:
      - ''
      - '&#dff9fbIncrease slots limit on'
      - '&#dff9fban item.'
      - ''
      - '&a+ &fʀᴀʀɪᴛʏ: &fऄ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 18.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Unique Slots Scroll':
    material: PAPER
    model_data: 14
    slot: 29
    display_name: "&fय Slots Scroll"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 18000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 18000'
      - '[console] ae giveitem %player_name% slotincreaser 1 UNIQUE'
      - "[message] &fए&#00d52a You just bought &fय Slots Scroll"
    lore:
      - ''
      - '&#dff9fbIncrease slots limit on'
      - '&#dff9fban item.'
      - ''
      - '&a+ &fʀᴀʀɪᴛʏ: &fअ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 18.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Elite Slots Scroll':
    material: PAPER
    model_data: 14
    slot: 30
    display_name: "&fय Slots Scroll"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 18000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 18000'
      - '[console] ae giveitem %player_name% slotincreaser 1 ELITE'
      - "[message] &fए&#00d52a You just bought &fय Slots Scroll"
    lore:
      - ''
      - '&#dff9fbIncrease slots limit on'
      - '&#dff9fban item.'
      - ''
      - '&a+ &fʀᴀʀɪᴛʏ: &fआ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 18.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Ultimate Slots Scroll':
    material: PAPER
    model_data: 14
    slot: 31
    display_name: "&fय Slots Scroll"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 18000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 18000'
      - '[console] ae giveitem %player_name% slotincreaser 1 ULTIMATE'
      - "[message] &fए&#00d52a You just bought &fय Slots Scroll"
    lore:
      - ''
      - '&#dff9fbIncrease slots limit on'
      - '&#dff9fban item.'
      - ''
      - '&a+ &fʀᴀʀɪᴛʏ: &fइ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 18.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'


  'Legendary Slots Scroll':
    material: PAPER
    model_data: 14
    slot: 32
    display_name: "&fय Slots Scroll"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 18000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 18000'
      - '[console] ae giveitem %player_name% slotincreaser 1 LEGENDARY'
      - "[message] &fए&#00d52a You just bought &fय Slots Scroll"
    lore:
      - ''
      - '&#dff9fbIncrease slots limit on'
      - '&#dff9fban item.'
      - ''
      - '&a+ &fʀᴀʀɪᴛʏ: &fई'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 18.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'


  'Fabled Slots Scroll':
    material: PAPER
    model_data: 14
    slot: 33
    display_name: "&fय Slots Scroll"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 18000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 18000'
      - '[console] ae giveitem %player_name% slotincreaser 1 FABLED'
      - "[message] &fए&#00d52a You just bought &fय Slots Scroll"
    lore:
      - ''
      - '&#dff9fbIncrease slots limit on'
      - '&#dff9fban item.'
      - ''
      - '&a+ &fʀᴀʀɪᴛʏ: &fउ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 18.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Lethal Slots Scroll':
    material: PAPER
    model_data: 14
    slot: 34
    display_name: "&fय Slots Scroll"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 18000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 18000'
      - '[console] ae giveitem %player_name% slotincreaser 1 LETHAL'
      - "[message] &fए&#00d52a You just bought &fय Slots Scroll"
    lore:
      - ''
      - '&#dff9fbIncrease slots limit on'
      - '&#dff9fban item.'
      - ''
      - '&a+ &fʀᴀʀɪᴛʏ: &fऊ'
      - '&a+ &fʙᴜʏ ᴘʀɪᴄᴇ:&7 18.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

      
  'Back':
    material: PAPER
    model_data: 99
    slot: 49
    display_name: "&#e55039ʙᴀᴄᴋ ᴛᴏ ᴇɴᴄʜᴀɴᴛᴇʀ"
    click_commands:
      - '[sound] BLOCK_COMPARATOR_CLICK'
      - '[player] enchanter'
  'Previous':
    material: PAPER
    model_data: 99
    slots: [47,48]
    display_name: "&#f0932bᴘʀᴇᴠɪᴏᴜs ᴘᴀɢᴇ"
    lore:
      - '&fᴘᴀɢᴇ: &72/2'
    click_commands:
      - '[openguimenu] EnchantShop'
  'Next':
    material: PAPER
    model_data: 99
    slots: [50,51]
    display_name: "&#00d52aɴᴇxᴛ ᴘᴀɢᴇ"
    lore:
      - '&fᴘᴀɢᴇ: &72/2'
