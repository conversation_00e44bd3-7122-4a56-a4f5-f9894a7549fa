menu_title: '&f:offset_-8::skills_1:'
open_command:
  - skills
  - skill
  - abilities
open_commands:
  - '[sound] BLOCK_NOTE_BLOCK_PLING'
size: 54
update_interval: 1
items:
  '1':
    material: PAPER
    model_data: 1001
    display_name: '&a&lFarming &a%aureliumskills_farming_roman%'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Harvest crops to &f&nearn&7 Farming XP.'
      - ''
      - '&aStats Leveled:'
      - ' &c ❤ &fʜᴇᴀʟᴛʜ'
      - ' &4 ➽ &fsᴛʀᴇɴɢʜᴛ'
      - ''
      - '&aInformation:'
      - '  &f• &fʟᴇᴠᴇʟ: &a%aureliumskills_farming_roman%'
      - '  &f• &fᴘʀᴏɢʀᴇss: &d%aureliumskills_xp_int_farming%/%aureliumskills_xp_required_farming% &8(%aureliumskills_xp_progress_1_farming%%)'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view your progression.'
    left_click_commands:
      - '[player] farming'
    right_click_commands:
      - '[player] farming'
    slot: 19
  '2':
    material: PAPER
    model_data: 1002
    display_name: '&a&lForaging &a%aureliumskills_foraging_roman%'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Cut trees to &f&nearn&7 Foraging XP.'
      - ''
      - '&aStats Leveled:'
      - ' &4 ➽ &fsᴛʀᴇɴɢʜᴛ'
      - ' &5 ✦ &fᴛᴏᴜɢʜɴᴇss'
      - ''
      - '&aInformation:'
      - '  &f• &fʟᴇᴠᴇʟ: &a%aureliumskills_foraging_roman%'
      - '  &f• &fᴘʀᴏɢʀᴇss: &d%aureliumskills_xp_int_foraging%/%aureliumskills_xp_required_foraging% &8(%aureliumskills_xp_progress_1_foraging%%)'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view your progression.'
    left_click_commands:
      - '[player] foraging'
    right_click_commands:
      - '[player] foraging'
    slot: 22
  '3':
    material: PAPER
    model_data: 1003
    display_name: '&a&lMining &a%aureliumskills_mining_roman%'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Mine stone and ores to &f&nearn&7 Mining XP.'
      - ''
      - '&aStats Leveled:'
      - ' &5 ✦ &fᴛᴏᴜɢʜɴᴇss'
      - ' &2 ☘ &fʟᴜᴄᴋ'
      - ''
      - '&aInformation:'
      - '  &f• &fʟᴇᴠᴇʟ: &a%aureliumskills_mining_roman%'
      - '  &f• &fᴘʀᴏɢʀᴇss: &d%aureliumskills_xp_int_mining%/%aureliumskills_xp_required_mining% &8(%aureliumskills_xp_progress_1_mining%%)'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view your progression.'
    left_click_commands:
      - '[player] mining'
    right_click_commands:
      - '[player] mining'
    slot: 25
  'next_page':
    material: MAP
    model_data: 1000
    slot: 44
    priority: 1
    update: true
    display_name: '&a&lNext Page'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[openguimenu] skills_2'
    right_click_commands:
      - '[openguimenu] skills_2'
  'skills':
    material: LEGACY_BOOK_AND_QUILL
    slot: 47
    priority: 1
    update: true
    display_name: '&a&lYour Skills'
    lore:
      - '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ'
      - ''
      - '&7Upgrade &b&lsᴋɪʟʟs &7by doing various'
      - '&7tasks to unlock valuable stat boosts,'
      - '&7abilities and more!'
      - ''
    left_click_commands:
      - '[none]'
    right_click_commands:
      - '[none]'
  'close':
    material: MAP
    model_data: 1000
    slot: 49
    priority: 1
    update: true
    display_name: '&c&lClose'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[close]'
    right_click_commands:
      - '[close]'
  'stats':
    material: head-%player_name%
    slot: 51
    priority: 1
    update: true
    display_name: '&a&lYour Stats'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Earn stat &dbuffs &7by'
      - '&7level up skills!'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view.'
    left_click_commands:
      - '[openguimenu] skills_stats_1'
    right_click_commands:
      - '[openguimenu] skills_stats_1'