# Aurelium EEhreedsf 1723578655 true Skills message file
abilities:
  farming:
    bountiful_harvest:
      name: 'ʙᴏᴜɴᴛɪғᴜʟ ʜᴀʀᴠᴇsᴛ'
      desc: '&a{value}% &7chance to get &f&ndouble&7 drops\n&7   from crops.'
      info: '{value}% 2x Drops'
    farmer:
      name: 'ғᴀʀᴍᴇʀ'
      desc: 'Earn &a{value}% &7more Farming XP.'
      info: '+{value}% XP'
    scythe_master:
      name: 'sᴄʏᴛʜᴇ ᴍᴀsᴛᴇʀ'
      desc: 'Increases damage from hoes by &a{value}%&7.'
      info: '+{value}% Hoe Damage'
    geneticist:
      name: 'ɢᴇɴᴇᴛɪᴄɪsᴛ'
      desc: 'Increases saturation gain from\n&7   plant-based foods by &a{value}&7.'
      info: '+{value} Saturation'
    triple_harvest:
      name: 'ᴛʀɪᴘʟᴇ ʜᴀʀᴠᴇsᴛ'
      desc: '&a{value}%&7 chance to get triple drops from\n&7   crops.'
      info: '{value}% 3x Drops'
  foraging:
    lumberjack:
      name: 'ʟᴜᴍʙᴇʀᴊᴀᴄᴋ'
      desc: '&a{value}%&7 chance to get &f&ndouble&7 drops\n&7   from logs.'
      info: '{value}% 2x Drops'
    forager:
      name: 'ғᴏʀᴀɢᴇʀ'
      desc: 'Earn &a{value}% &7more Foraging XP.'
      info: '+{value}% XP'
    axe_master:
      name: 'ᴀxᴇ ᴍᴀsᴛᴇʀ'
      desc: 'Increases damage from axes by &a{value}%&7.'
      info: '+{value}% Axe Damage'
    valor:
      name: 'ᴠᴀʟᴏʀ'
      desc: 'Grants &4+{value} Strength&7 while holding\n&7   an axe.'
      info: '+{value} Strength'
    shredder:
      name: 'sʜʀᴇᴅᴅᴇʀ'
      desc: '&a{value}%&7 chance to deal &f&ntriple&7 durability\n&7   damage with axes.'
      info: '{value}% 3x Durability Damage'
  mining:
    lucky_miner:
      name: 'ʟᴜᴄᴋʏ ᴍɪɴᴇʀ'
      desc: '&a{value}%&7 chance to get &f&ndouble&7 drops\n&7   from ores.'
      info: '{value}% 2x Drops'
    miner:
      name: 'ᴍɪɴᴇʀ'
      desc: 'Earn &a{value}%&7 more Mining XP.'
      info: '+{value}% XP'
    pick_master:
      name: 'ᴘɪᴄᴋ ᴍᴀsᴛᴇʀ'
      desc: 'Deal &a{value}%&7 more damage with pickaxes.'
      info: '+{value}% Pickaxe Damage'
    stamina:
      name: 'sᴛᴀᴍɪɴᴀ'
      desc: 'Grants &5+{value} Toughness&7 while\n&7   holding a pickaxe'
      info: '+{value} Toughness'
    hardened_armor:
      name: 'ʜᴀʀᴅᴇɴᴇᴅ ᴀʀᴍᴏʀ'
      desc: '&a{value}%&7 chance to &f&nnegate&7 durability\n&7   damage on armor.'
      info: '{value}% No Durability Loss'
  fishing:
    lucky_catch:
      name: 'ʟᴜᴄᴋʏ ᴄᴀᴛᴄʜ'
      desc: '&a{value}% &7added chance to get &f&ndouble&7 drops\n&7   from fishing.'
      info: '{value}% 2x Drops'
    fisher:
      name: 'ғɪsʜᴇʀ'
      desc: 'Earn &a{value}%&7 more Fishing XP.'
      info: '+{value}% XP'
    treasure_hunter:
      name: 'ᴛʀᴇᴀsᴜʀᴇ ʜᴜɴᴛᴇʀ'
      desc: '&a{value}%&7 higher chance to get rare loot from fishing.'
      info: '+{value}% Rare Loot'
    grappler:
      name: 'ɢʀᴀᴘᴘʟᴇʀ'
      desc: 'Hook entities with &a{value}%&7 more speed.'
      info: '+{value}% Hook Speed'
    epic_catch:
      name: 'ᴇᴘɪᴄ ᴄᴀᴛᴄʜ'
      desc: '&a{value}%&7 added chance to get epic loot\n&7   from fishing.'
      info: '+{value}% Epic Loot'
  excavation:
    metal_detector:
      name: 'ᴍᴇᴛᴀʟ ᴅᴇᴛᴇᴄᴛᴏʀ'
      desc: '&a{value}%&7 added chance to get rare loot from digging.'
      info: '+{value}% Rare Loot'
    excavator:
      name: 'ᴇxᴄᴀᴠᴀᴛᴏʀ'
      desc: 'Earn &a{value}%&7 more Excavation XP.'
      info: '+{value}% XP'
    spade_master:
      name: 'sᴘᴀᴅᴇ ᴍᴀsᴛᴇʀ'
      desc: 'Deal &a{value}%&7 more damage with shovels.'
      info: '+{value}% Shovel Damage'
    bigger_scoop:
      name: 'ʙɪɢɢᴇʀ sᴄᴏᴏᴘ'
      desc: '&a{value}%&7 chance to get &f&ntriple&7 drops from digging.'
      info: '{value}% 3x Drops'
    lucky_spades:
      name: 'ʟᴜᴄᴋʏ sᴘᴀᴅᴇs'
      desc: '&a{value}%&7 added chance to get epic loot from digging.'
      info: '+{value}% Epic Loot'
  archery:
    crit_chance:
      name: 'ᴄʀɪᴛ ᴄʜᴀɴᴄᴇ'
      desc: '&a+{value}% &7chance to deal a &4critical&7 hit.'
      info: '+{value}% Crit Chance'
    archer:
      name: 'ᴀʀᴄʜᴇʀ'
      desc: 'Earn &a{value}%&7 more Archery XP.'
      info: '+{value}% XP'
    bow_master:
      name: 'ʙᴏᴡ ᴍᴀsᴛᴇʀ'
      desc: 'Deal &a{value}%&7 more damage with bows.'
      info: '+{value}% Bow Damage'
    piercing:
      name: 'ᴘɪᴇʀᴄɪɴɢ'
      desc: '&a{value}%&7 chance for arrows to\n&7   pierce through mobs.'
      info: '{value}% Pierce Chance'
    stun:
      name: 'sᴛᴜɴ'
      desc: '&a{value}% &7chance to &cdecreases&7 enemy\n&7   movement speed by &a20%&7 for 2 seconds.'
      info: '{value}% Stun Chance'
  defense:
    shielding:
      name: 'sʜɪᴇʟᴅɪɴɢ'
      desc: 'Reduce incoming damage by &a{value}%\n&7   while sneaking.'
      info: '-{value}% Incoming Dmg Sneaking'
    defender:
      name: 'ᴅᴇғᴇɴᴅᴇʀ'
      desc: 'Earn {value}% more Defense XP'
      info: '+{value}% XP'
    mob_master:
      name: 'ᴍᴏʙ ᴍᴀsᴛᴇʀ'
      desc: 'Reduce incoming damage from mobs by &a{value}%&7.'
      info: '-{value}% Mob Dmg'
    immunity:
      name: 'ɪᴍᴍᴜɴɪᴛʏ'
      desc: '{value}% chance to completely negate\n&7   an enemy attack.'
      info: '{value}% Immune'
    no_debuff:
      name: 'ɴᴏ ᴅᴇʙᴜғғ'
      desc: '&a{value}%&7 chance to negate a harmful\n&7   potion effect from being applied.'
      info: '{value}% No Debuff'
  fighting:
    crit_damage:
      name: 'ᴄʀɪᴛ ᴅᴀᴍᴀɢᴇ'
      desc: 'Increases &4critical&7 hit damage by &a{value}%&7'
      info: '+{value}% Crit Damage'
    fighter:
      name: 'ғɪɢʜᴛᴇʀ'
      desc: 'Earn &a{value}%&7 more Fighting XP.'
      info: '+{value}% XP'
    sword_master:
      name: 'sᴡᴏʀᴅ ᴍᴀsᴛᴇʀ'
      desc: 'Deal &a{value}%&7 more damage with swords.'
      info: '+{value}% Sword Damage'
    first_strike:
      name: 'ғɪʀsᴛ sᴛʀɪᴋᴇ'
      desc: 'Deal &a{value}%&7 more damage on the first hit.'
      info: '+{value}% Dmg First Hit'
      dealt: '&cFirst Strike Dealt!'
    bleed:
      name: 'ʙʟᴇᴇᴅ'
      desc: '&a{value}% &7chance to make the enemy &4bleed&7 for\n&7   3 bleed ticks, dealing &c{value_2}&7 damage per tick.'
      info: '{value}% Chance, {value_2} Damage'
      enemy_bleeding: '&cEnemy Bleeding!'
      self_bleeding: '&cYou are bleeding!'
      stop: '&fThe bleeding has stopped'
  endurance:
    anti_hunger:
      name: 'ᴀɴᴛɪ ʜᴜɴɢᴇʀ'
      desc: '&a{value}%&7 chance to stop hunger loss.'
      info: '{value}% No Hunger Loss'
    runner:
      name: 'ʀᴜɴɴᴇʀ'
      desc: 'Earn &a{value}%&7 more Endurance XP.'
      info: '+{value}% XP'
    golden_heal:
      name: 'ɢᴏʟᴅᴇɴ ʜᴇᴀʟ'
      desc: 'Increases regeneration effect\n&7   health gain by {value}%'
      info: '+{value}% Effect Regen'
    recovery:
      name: 'ʀᴇᴄᴏᴠᴇʀʏ'
      desc: 'Increases natural regeneration when\n&7   at less than half health by &a{value}%&7.'
      info: '+{value}% Low HP Regen'
    meal_steal:
      name: 'ᴍᴇᴀʟ sᴛᴇᴀʟ'
      desc: '&a{value}%&7 chance to steal 1 hunger point\n&7   when attacking a player.'
      info: '{value}% Hunger Steal'
  agility:
    light_fall:
      name: 'ʟɪɢʜᴛ ғᴀʟʟ'
      desc: 'Reduces all fall damage by &a{value}%&7.'
      info: '-{value}% Fall Damage'
    jumper:
      name: 'ᴊᴜᴍᴘᴇʀ'
      desc: 'Earn &a{value}% &7more Agility XP.'
      info: '+{value}% XP'
    sugar_rush:
      name: 'sᴜɢᴀʀ ʀᴜsʜ'
      desc: 'Consumed and splashed speed and jump boost\n&7   potions last &a{value}%&7 longer.'
      info: '{value}% Longer Speed'
    fleeting:
      name: 'ғʟᴇᴇᴛɪɴɢ'
      desc: 'Gain &a+{value}%&7 movement speed when below &c20% health&7.'
      info: '+{value}% Speed'
      start: '&fYou have gained a fleeting speed boost! &7(+{value}% Speed)'
      end: '&7Fleeting speed boost has worn off'
    thunder_fall:
      name: 'ᴛʜᴜɴᴅᴇʀ ғᴀʟʟ'
      desc: 'When sneaking during a fall, you have a &a{value}%&7 chance\n&7   to deal &a{value_2}%&7 of the fall damage expected to mobs\n&7   in a 3 block radius.'
      info: '{value}% Chance, {value_2}% Damage'
  alchemy:
    alchemist:
      name: 'ᴀʟᴄʜᴇᴍɪsᴛ'
      desc: 'Potions you brew have a &a{value}%&7 longer\n&7   duration.'
      info: '+{value}% Potion Duration'
      lore: '&a(+{duration})'
    brewer:
      name: 'ʙʀᴇᴡᴇʀ'
      desc: 'Earn &a{value}%&7 more Alchemy XP'
      info: '+{value}% XP'
    splasher:
      name: 'sᴘʟᴀsʜᴇʀ'
      desc: 'Potions you splash gain &a{value}%&7 longer\n&7   duration for each player affected.'
      info: '+{value}% Duration/Player'
    lingering:
      name: 'ʟɪɴɢᴇʀɪɴɢ'
      desc: 'Lingering potions naturally decay &a{value}%\n&7   slower and decay &a{value_2}% &7less per affected entity.'
      info: '-{value}% Natural, -{value_2}% Entity Decay'
    wise_effect:
      name: 'ᴡɪsᴇ ᴇғғᴇᴄᴛ'
      desc: 'Gain &9{value} Wisdom &7for every unique active\n&7   potion effect.'
      info: '+{value} Wisdom/Effect'
  enchanting:
    xp_convert:
      name: 'xᴘ ᴄᴏɴᴠᴇʀᴛ'
      desc: 'Earn 1 Minecraft experience for every &d{value}\n&7   skill XP you gain.'
      info: '1 EXP/{value} Skill XP'
    enchanter:
      name: 'ᴇɴᴄʜᴀɴᴛᴇʀ'
      desc: 'Earn &a{value}% &7more Enchanting XP.'
      info: '+{value}% XP'
    xp_warrior:
      name: 'xᴘ ᴡᴀʀʀɪᴏʀ'
      desc: 'Mobs you kill have a &a{value}%&7 chance of\n&7   dropping double experience.'
      info: '{value}% 2x Mob XP'
    enchanted_strength:
      name: 'ᴇɴᴄʜᴀɴᴛᴇᴅ sᴛʀᴇɴɢᴛʜ'
      desc: 'Gain &4{value} Strength&7 for every unique\n&7   enchantment on the item held.'
      info: '{value} Strength/Enchant'
    lucky_table:
      name: 'ʟᴜᴄᴋʏ ᴛᴀʙʟᴇ'
      desc: 'Items you enchant using the enchantment\n&7   table have a &a{value}%&7 chance of upgrading the enchantment\n&7   level by 1 if not max level.'
      info: '{value}% Enchant Upgrade Chance'
  healing:
    life_essence:
      name: 'ʟɪғᴇ ᴇssᴇɴᴄᴇ'
      desc: 'Instant Health potions heal &a{value}% &7more.'
      info: '+{value}% Instant Health'
    healer:
      name: 'ʜᴇᴀʟᴇʀ'
      desc: 'Earn &a{value}%&7 more Healing XP.'
      info: '+{value}% XP'
    life_steal:
      name: 'ʟɪғᴇ sᴛᴇᴀʟ'
      desc: 'Heal &a{value}%&7 of the max HP of hostile mobs\n&7  and players you kill.'
      info: 'Steal {value}% HP'
    golden_heart:
      name: 'ɢᴏʟᴅᴇɴ ʜᴇᴀʀᴛ'
      desc: 'Damage done to your absorption hearts is\n&7   reduced by &a{value}%&7.'
      info: '-{value}% Absorption Dmg'
    revival:
      name: 'ʀᴇᴠɪᴠᴀʟ'
      desc: 'Gain &c+{value} Health &7and &6+{value_2} Regeneration\n&7   for 30 seconds after respawning.'
      info: '+{value} Health, +{value_2} Regen'
      message: '&fYou gained a revival bonus for 30 seconds! &f(&c+{value} Health, &6+{value_2} Regeneration&f)'
  forging:
    disenchanter:
      name: 'ᴅɪsᴇɴᴄʜᴀɴᴛᴇʀ'
      desc: Gain &a{value}%&7 more experience when disenchanting\n&7   items using a grindstone.
      info: '+{value}% Grindstone EXP'
    forger:
      name: 'ғᴏʀɢᴇʀ'
      desc: Earn &a{value}% &7more Forging XP.
      info: '+{value}% XP'
    repairing:
      name: 'ʀᴇᴘᴀɪʀɪɴɢ'
      desc: Repairing items in an anvil using the raw\n&7   material the item was made from repairs\n   &a{value}%&7 more durability.
      info: '+{value}% Anvil Repair'
    anvil_master:
      name: 'ᴀɴᴠɪʟ ᴍᴀsᴛᴇʀ'
      desc: Increases the maximum repair cost of an anvil\n&7   before it is too expensive to &a{value} experience&7.
      info: '{value} Max Cost'
    skill_mender:
      name: 'sᴋɪʟʟ ᴍᴇɴᴅᴇʀ'
      desc: Gaining skill XP has a &a{value}%&7 chance to repair\n&7   a held item or worn armor piece with the Mending\n&7   enchantment by 1 durability per 2 skill XP.
      info: '{value}% Mend Chance'
acf:
  core:
    permission_denied: '&8[&a&l!&8] &7I''m sorry, but you do not have permission to perform this command.'
    permission_denied_parameter: '&8[&a&l!&8] &7I''m sorry, but you do not have permission to perform this command.'
    error_generic_logged: '&8[&a&l!&8] &7An error occurred. This problem has been logged. Sorry for the inconvenience.'
    unknown_command: '&8[&a&l!&8] &7Unknown Command, please type &a/sk help&7.'
    invalid_syntax: '&8[&a&l!&8] &7Usage: &a{command} {syntax}&7.'
    error_prefix: '{message}'
    error_performing_command: "&8[&a&l!&8] &7I'm sorry, but there was an error performing this command."
    info_message: '{message}'
    please_specify_one_of: '&8[&a&l!&8] &7Please specify one of &a{valid}&7.'
    must_be_a_number: '&8[&a&l!&8] &a{num}&7 must be a number.'
    must_be_min_length: '&8[&a&l!&8] &7Must be at least &a{min} &7characters long.'
    must_be_max_length: '&8[&a&l!&8] &7Must be at most &a{max} &7characters long.'
    please_specify_at_most: '&8[&a&l!&8] &7Please specify a value at most &a{max}&7.'
    please_specify_at_least: '&8[&a&l!&8] &7Please specify a value at least &a{min}&7.'
    not_allowed_on_console: '&8[&a&l!&8] &7Console may not execute this command.'
    could_not_find_player: '&8[&a&l!&8] &7Could not find a player by the name: &e{search}'
    no_command_matched_search: '&8[&a&l!&8] &7No command matched &a{search}&7.'
    help_page_information: '&b - Showing page &a{page} &bof &a{totalpages} &b(&e{results}&b results).'
    help_no_results: '&bError: No more results.'
    help_header: '&e=== &bShowing help for &a{commandprefix}{command}&e ==='
    help_format: '&b{commandprefix}{command} &a{parameters} &e{separator} {description}'
    help_detailed_header: '&e=== &bShowing detailed help for &a{commandprefix}{command}&e ==='
    help_detailed_command_format: '&b{command} &a{parameters} &e{separator} {description}'
    help_detailed_parameter_format: '&a{syntaxorname}: &e{description}'
    help_search_header: '&e=== &bSearch results for &a{commandprefix}{command} {search}&e ==='
  minecraft:
    invalid_world: '&8[&a&l!&8] &7That world does not exist.'
    you_must_be_holding_item: '&8[&a&l!&8] &7You must be holding an item in your main hand.'
    player_is_vanished_confirm: '&8[&a&l!&8] &a{vanished} &7is vanished. Do not blow their cover!\n&fTo confirm your action add &a:confirm &7to the end of their name.\n&eExample: {vanished}:confirm'
    username_too_short: '&8[&a&l!&8] &7Username too short, must be at least three characters.'
    is_not_a_valid_name: '&8[&a&l!&8] &a{name} &7is not a valid username.'
    multiple_players_match: '&8[&a&l!&8] &7Multiple players matched &b{search} &7({all})&7, please be more specific.'
    no_player_found_server: '&8[&a&l!&8] &7No player matching &e{search} &7is connected to this server.'
    no_player_found_offline: '&8[&a&l!&8] &7No player matching &e{search}&7 could be found.'
    no_player_found: '&8[&a&l!&8] &7No player matching &e{search}&7 could be found.'
    location_please_specify_world: '&8[&a&l!&8] &7Please specify world. Example: &eworld:x,y,z&7.'
    location_please_specify_xyz: '&8[&a&l!&8] &7Please specify the coordinates x, y, and z. Example: &eworld:x,y,z&7.'
    location_console_not_relative: '&8[&a&l!&8] &7Console may not use relative coordinates for location.'
action_bar:
  idle: '&c{hp}/{max_hp}❤                 &b{mana}/{max_mana}☄ {mana_unit}'
  xp: '&c{hp}/{max_hp}❤  &a+{xp_gained} {skill} {xp_unit} &7({current_xp}/{level_xp} {xp_unit})  &b{mana}/{max_mana} {mana_unit}'
  xp_removed: '&c{hp}/{max_hp}❤  &a-{xp_removed} {skill} {xp_unit} &7({current_xp}/{level_xp} {xp_unit})  &b{mana}/{max_mana} {mana_unit}'
  maxed: '&c{hp}/{max_hp}❤  &a+{xp_gained} {skill} {xp_unit} &c(MAXED!)  &b{mana}/{max_mana} {mana_unit}'
  maxed_removed: '&c{hp}/{max_hp}❤  &a-{xp_removed} {skill} {xp_unit} &c(MAXED!)  &b{mana}/{max_mana} {mana_unit}'
  ability: '{message}'
  boss_bar_xp: '&a{skill} {level} &7({current_xp}/{level_xp} {xp_unit})'
  boss_bar_maxed: '&a{skill} {level} &c(MAXED!)'
commands:
  prefix: '&8[&a&l!&8] &7'
  armor:
    modifier:
      add:
        added: '&a{stat} &7modifier with level &b{value} &7was added to your item.'
        already_exists: '&7An armor modifier with stat &a{stat} &7already exists on your armor! Remove it first with &a/sk armor modifier remove&7.'
        lore: '&7{stat}: {color}+{value}'
        lore_subtract: '&7{stat}: {color}-{value}'
      remove:
        does_not_exist: '&7Your item does not have a &a{stat} &7modifier!'
        removed: '&a{stat} &7modifier was removed from your item.'
      list:
        header: '&8[&a&l!&8] &7Armor modifiers found:'
        entry: '  &f• &f{stat}: &a{value}'
      removeall:
        removed: '&7All armor modifiers have been removed from your item.'
    multiplier:
      add:
        added: '&a{target} &7multiplier with value &b{value} &7was added to your item.'
        already_exists: '&7An armor multiplier with target &a{target} &7already exists on your armor! Remove it first with &a/sk armor multiplier remove&7.'
        skill_lore: '&7When worn: &a+{value}% {skill} XP.'
        skill_lore_subtract: '&7When worn: &a-{value}% {skill} XP.'
        global_lore: '&7When worn: &9+{value}% Skill XP.'
        global_lore_subtract: '&7When worn: &9-{value}% Skill XP.'
      remove:
        does_not_exist: '&7Your item does not have a &a{target} &7armor multiplier!'
        removed: '&a{target} &7armor multiplier was removed from your item.'
      list:
        header: '&8[&a&l!&8] &7Armor multipliers found:'
        entry: '  &f• &f{target}: &a{value}'
      removeall:
        removed: '&7All armor multipliers have been removed from your item.'
    requirement:
      add:
        added: '&a{skill}&7 armor requirement with level &b{level}&7 was added to your item.'
        already_exists: '&7Item already has a &a{skill}&7 armor requirement! Remove it first with &a/sk armor requirement remove&7.'
        lore: '&7Requires &a{skill} {level}&7.'
      remove:
        does_not_exist: '&7Your item does not have a &a{skill}&7 armor requirement!'
        removed: '&a{skill}&7 armor requirement was removed from your item'
      list:
        header: '&8[&a&l!&8] &7Armor requirements found:'
        entry: '  &f• &f{skill}: &a{level}'
      removeall:
        removed: '&7All armor requirements have been removed from your item'
      equip: '&7You do &cnot&7 meet the requirements to equip this item: {requirements}'
      entry: '&a{skill} {level}&7, '
  backup:
    load:
      confirm: '&7Loading a backup will revert all skill data of players. This action is permanent and cannot be undone. You may want to save a backup before loading one. Type the command again to confirm this action'
      loading: '&7Loading backup...'
      loaded: '&7Successfully loaded backup'
      error: '&7Error loading backup: &c{error}'
      must_be_yaml: '&7File must be a &a.yml&7 file!'
      file_not_found: '&7A backup of this file name does not exist in the backups folder!'
    save:
      saving: '&7Saving backup...'
      saved: '&7Backed up &a{type} &7data as &a{file}&7'
      error: '&7Error backing up &a{type}&7 data! See error in console/below for details.'
  claimitems:
    no_items: '&7You have no items to claim.'
  item:
    give:
      sender: '&7Gave &b{amount}x {key} &7to &a{player}&7.'
      receiver: '&7You were given &b{amount}x {key}&7.'
    modifier:
      add:
        added: '&a{stat} &7modifier with level &b{value} &7was added to your item.'
        already_exists: '&7An item modifier with stat {color}{stat} &7already exists on your armor! Remove it first with &a/sk item modifier remove&7.'
        lore: '&7{stat}: {color}+{value}'
        lore_subtract: '&7{stat}: {color}-{value}'
      remove:
        does_not_exist: '&7Your item does not have a {color}{stat} &7modifier!'
        removed: '&a{stat} &7modifier was removed from your item.'
      list:
        header: '&8[&a&l!&8] &7Item modifiers found:'
        entry: '  &f• &f{stat}: &a{value}'
      removeall:
        removed: '&7All item modifiers have been removed from your item.'
    multiplier:
      add:
        added: '&a{target} &7multiplier with value &b{value} &7was added to your item.'
        already_exists: '&7An item multiplier with target &b{target} &7already exists on your item! Remove it first with &a/sk item multiplier remove&7.'
        skill_lore: '&7When held: &a+{value}% {skill} XP.'
        skill_lore_subtract: '&7When held: &a-{value}% {skill} XP.'
        global_lore: '&7When held: &9+{value}% Skill XP.'
        global_lore_subtract: '&7When held: &9-{value}% Skill XP.'
      remove:
        does_not_exist: '&7Your item does not have a &b{target} &7item multiplier!'
        removed: '&a{target} &7item multiplier was removed from your item.'
      list:
        header: '&8[&a&l!&8] &7Item multipliers found:'
        entry: '  &f• &f{target}: &a{value}'
      removeall:
        removed: '&7All item multipliers have been removed from your item.'
    register:
      registered: '&7Registered held item to key &b{key}'
      already_registered: '&7There is already an item registered to key &f&n{key}&7, use &a/skills item unregister &7to unregister it first.'
      no_spaces: '&7Item key cannot contain spaces!'
    requirement:
      add:
        added: '&a{skill}&7 item requirement with level &b{level}&7 was added to your item.'
        already_exists: '&7Item already has a &a{skill}&7 item requirement! Remove it first with &a/sk item requirement remove&7.'
        lore: '&7Requires &a{skill} {level}&7.'
      remove:
        does_not_exist: '&7Your item does not have a &a{skill}&7 item requirement!'
        removed: '&a{skill}&7 item requirement was removed from your item.'
      list:
        header: '&8[&a&l!&8] &7Item requirements found:'
        entry: '  &f• &f{skill}: &a{level}'
      removeall:
        removed: '&7All item requirements have been removed from your item.'
      use: '&7You do not meet the requirements to use this item: {requirements}'
      entry: '&a{skill} {level}&7, '
    unregister:
      unregistered: '&7Unregistered item with key &b{key}&7.'
      not_registered: '&7There is no item registered with key &b{key}&7.'
  lang:
    set: '&7Language set to &e{lang}&7.'
    not_found: '&7Language not found!'
  mana:
    display: '&7Your {mana_unit}: &b{current}/{max}&7.'
    display_other: '&7{mana_unit} of &a{player}&7: &b{current}/{max}'
    add: '&7Added &b{amount} {mana_unit} &7to player &a{player}'
    remove: '&7Removed &b{amount} {mana_unit} &7from player &a{player}'
    set: '&7Set player &a{player} &7to &b{amount} {mana_unit}'
    at_least_zero: '&b{mana_unit}&7 must be at least zero!'
    console_specify_player: '&7Console must specify a player!'
  modifier:
    add:
      added: '{color}{stat} &7modifier level &b{value} &7with name &f&n{name}&7 added to player &a{player}&7.'
      already_exists: '&7Modifier with name &f&n{name}&7 already exists on player &a{player}&7! Remove it with &a/sk modifier remove {player} {name}&7.'
    remove:
      not_found: '&7Modifier &b{name}&7 not found for player &a{player}&7.'
      removed: '&7Modifier &b{name} &7removed from player &a{player}&7.'
    list:
      all_stats_header: '&8[&a&l!&8] &7Stat modifiers for player &a{player}:'
      all_stats_entry: '  &f• &f{name}: {color}{stat} &aLevel {value}&7.'
      one_stat_header: '&7Stat modifiers ({color}{stat}&7) for player &a{player}:'
      one_stat_entry: '  &f{name} - {color}{stat} &fLevel &6{value}'
      players_only: '&eOnly players can list their own modifiers! Try specifying a player'
    removeall:
      removed_all_stats: '&7Removed &b{num} &7stat modifiers from player &a{player}'
      removed_one_stat: '&7Removed &b{num} {color}{stat} &7modifiers from player &a{player}'
      players_only: '&eOnly players can remove their own modifiers! Try specifying a player'
  multiplier:
    global: Global
    list: '&7Global XP multiplier for &a{player}&7: &b{multiplier}x / +{percent}%'
    skill_entry: '&b{skill} &7XP multiplier: &b{multiplier}x / +{percent}%'
    players_only: '&7Only players can list their own multiplier! Specify a player!'
  profile:
    skills: 'Aurelium Skills profile for &6{name}:\n&7UUID: &f{uuid}\n&7Skills:{skill_entries}'
    skill_entry: '\n  &3{skill} &7- Level: &f{level}&7, XP: &f{xp}'
    stats: 'Aurelium Skills stats profile for &6{name}:\n&7UUID: &f{uuid}\n&7Stats:{stat_entries}'
    stat_entry: '\n  &3{stat} &7- Total Level: &f{total_level}&7, Base Level: &f{base_level}&7, Modified Level: &f{modified_level}'
  rank:
    header: '&8[&a&l!&8] &7Your Skill Rankings'
    power: '&fPower &7(All Skills): &a#{rank} of {total}'
    entry: '  &f• &f{skill}: &a#{rank} of {total}'
  reload: '&7Config reloaded!'
  save:
    already_saving: '&7Data is already saving!'
    mysql_not_enabled: '&7MySQL was not enabled at server startup, saving to file instead!'
    saved: '&7Skill data saved!'
  skill:
    reset:
      reset_all: '&7All Skills set to level &b1 &7for player &a{player}'
      reset_skill: '&7Skill &b{skill} &7set to level &b1 &7for player &a{player}'
    setall:
      at_least_one: '&eLevel must be at least 1!'
      set: '&7All skills set to level &b{level} &7for player &a{player}'
    setlevel:
      at_least_one: '&eLevel must be at least 1!'
      set: '&7Skill &b{skill} &7set to level &b{level} &7for player &a{player}'
  toggle:
    enabled: '&7Your skills action bar has been &aenabled&7.'
    disabled: '&7Your skills action bar has been &cdisabled&7.'
    not_enabled: '&7Skills action bar is not enabled on this server!'
  top:
    power_entry: '&f{rank}. &b{player} &f- {level}'
    power_header: '&f--==&b&lAll Skills Leaderboard&f==--'
    power_header_page: '&f--==&b&lAll Skills Leaderboard &f(Page {page})==--'
    skill_header: '&f--==&b&l{skill} Leaderboard&f==--'
    skill_header_page: '&f--==&b&l{skill} Leaderboard &f(Page {page})==--'
    skill_entry: '&f{rank}. &b{player} &f- {level}'
    average_header: '&f--==&b&lSkill Average Leaderboard&f==--'
    average_header_page: '&f--==&b&lSkill Average Leaderboard &f(Page {page})==--'
    average_entry: '&f{rank}. &b{player} &f- {level}'
    usage: '&eUsage: &a/sk top &f<page> &7or &a/sk top &f[skill] <page>'
  transfer:
    success: '&7Successfully transferred player data from &a{from} &7to &a{to}'
    error: '&7There was an error transferring player data. Check console for errors.'
  updateleaderboards:
    already_updating: '&7Leaderboard is already updating!'
    updated: '&7Leaderboard updated!'
  version: '&7You are on Aurelium Skills version &b{current_version}&7. The latest version is &b{latest_version}'
  xp:
    add: '&7Added &b{amount} {skill} &7XP to player &a{player}'
    set: '&7Set &b{skill} &7XP for player &a{player} &7to &a{amount}'
    remove: '&8[&a&l!&8] &7Removed &b{amount} {skill} &7XP from player &a{player}'
  unknown_skill: '&8[&a&l!&8] &7Unknown skill!'
  no_profile: '&8[&a&l!&8] &7Player does not have a skills profile. Please try rejoining the game or restarting the server.'
leveler:
  title: '&b{skill} &lLEVEL UP!'
  subtitle: '&7{old} ➜ &3&l{new}'
  level_up: '&7\n&f :skills_box:     &f&lSKILL LEVEL UP!\n&7                 &b{skill} &7{old}&7➜&3&l{new}\n&7{stat_level}{ability_unlock}{ability_level_up}{mana_ability_unlock}{mana_ability_level_up}{money_reward}\n&3'
  stat_level: '\n&7                  {color}+{num}{symbol} {stat}'
  ability_unlock: '\n&7                  &f• &fᴀʙɪʟɪᴛʏ ᴜɴʟᴏᴄᴋᴇᴅ: &a{ability}'
  ability_level_up: '\n&7                  &f• &fᴀʙɪʟɪᴛʏ ʟᴇᴠᴇʟ ᴜᴘ: &a{ability} &7(Level &f&n{level}&7)'
  mana_ability_unlock: '\n&7                  &f• &bᴍᴀɴᴀ ᴀʙɪʟɪᴛʏ ᴜɴʟᴏᴄᴋᴇᴅ: &9{mana_ability}'
  mana_ability_level_up: '\n&7                  &f• &bᴍᴀɴᴀ ᴀʙɪʟɪᴛʏ ʟᴇᴠᴇʟ ᴜᴘ: &9{mana_ability} &7(Level &f&n{level}&7)'
  money_reward: '\n&7                  &f• &fᴍᴏɴᴇʏ: &a${amount}'
  unclaimed_item: '&8[&a&l!&8] &7You have unclaimed items because your inventory was full, use &a/skills claimitems &7to claim them'
menus:
  common:
    close: '&c&lClose'
    level: '&aInformation:\n  &f• &fʟᴇᴠᴇʟ: &a{level}'
    progress_to_level: '&aProgress:\n&f  • &fɴᴇxᴛ ʟᴇᴠᴇʟ:&a {level}\n  &f• &fᴄᴜʀʀᴇɴᴛ xᴘ: &d{current_xp}/{level_xp} {xp_unit} &8({percent}%)'
    max_level: '&a&l✔ &fYou have reached the &a&lᴍᴀx&f level!'
    ability_levels: '\n&aAbility Levels:\n{ability_1}\n{ability_2}\n{ability_3}\n{ability_4}\n{ability_5}\n '
    ability_level_entry: ' &a✔ &f{ability} {level} &8({info})'
    ability_level_entry_locked: ' &8- &m{ability}'
    stats_leveled: '\n&aStats Leveled:\n  &f• &f{stats}'
    mana_ability: '\n&aMana Ability: &9{mana_ability} {level}\n  &7Duration: &a{duration}s &7Mana Cost: &b{mana_cost} &7Cooldown: &e{cooldown}s\n '
  skills_menu:
    skills_menu_title: Your Skills
    your_skills: '&bYour Skills - &a{player}'
    your_skills_desc: '&7Upgrade Skills by doing various tasks\n&7to unlock valuable stat boosts,\n&7abilities, and more!'
    your_skills_hover: '&eHover over a Skill for more information!'
    your_skills_click: '&eClick on a Skill to view level progression!'
    skill_click: '&eClick to view Level Progression!'
    skill_locked: '&cYou do no have permission to view this skill!'
    stats: '&bStats'
    stats_desc: '&7Earn stat buffs by leveling up skills'
    stats_click: '&eClick to learn more or view your stats'
  level_progression_menu:
    level_progression_menu_title: '&f:offset_-8::skills_progression:'
    your_ranking: '&a&lYour Skill Ranking'
    rank_out_of: '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ\n&7\n&7You are ranked &a{rank}&7 out of &a{total}&7 players.'
    rank_percent: '&7You are in the top &c{percent}%&7 of players.'
    back: '&c&lBack'
    back_click: '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    next_page: '&a&lNext Page'
    next_page_click: '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    previous_page: '&a&lPrevious Page'
    previous_page_click: '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    level_unlocked: '&a&lLevel {level}'
    level_in_progress: '&e&lLevel {level}'
    level_locked: '&c&lLevel {level}'
    level_number: '&8ʟᴇᴠᴇʟ {level}\n&7\n'
    unlocked: '&a&l✔ ʟᴇᴠᴇʟ &funlocked.'
    in_progress: '&e♻ &fLevel in &e&lᴘʀᴏɢʀᴇss&f.'
    locked: '&c&l✖ &fLevel &c&lɴᴏᴛ &freached.'
    rewards: '&aRewards:{rewards}'
    rewards_entry: '\n {color}+{num} {symbol} {stat}'
    money_reward: '\n &a${amount} &fᴍᴏɴᴇʏ'
    ability_unlock: '\n&aAbilities Gear:\n  &f• &f{ability} &8▏ &a&lᴀʙɪʟɪᴛʏ ᴜɴʟᴏᴄᴋ\n   &7{desc}\n '
    ability_level: '\n&aAbilities Gear:\n  &f• &f{ability} &7(Level {level})\n   &7{desc}\n '
    mana_ability_unlock: '\n  &f• &9{mana_ability} &8▏ &b&lᴍᴀɴᴀ ᴀʙɪʟɪᴛʏ ᴜɴʟᴏᴄᴋ\n   &7{desc}\n '
    mana_ability_level: '\n  &f• &9{mana_ability} &7(Level {level})\n   &7{desc}\n '
    progress: '&aInformation:\n  &f• &fᴘʀᴏɢʀᴇss: &d&7{current_xp}/{level_xp} {xp_unit} &8({percent}%)'
    leaderboard_click: '&a▶ &lᴄʟɪᴄᴋ &fto view leaderboard.'
    sources: '&a&lXP Sources'
    sources_desc: '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ\n&7\n&7The &f&nsources&7 menu shows all the ways\n&7you can earn &d&lxᴘ&7 in a skill, as well as\n&7how mucho &d&lxᴘ&7 each source gives.'
    sources_click: '&a▶ &lᴄʟɪᴄᴋ &fto view.'
    abilities: '&b&lAbilities'
    abilities_desc: '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ\n&7\n&7Abilities are &f&npassive&7 perks you\n&a&lᴜɴʟᴏᴄᴋ &7and &d&lᴜᴘɢʀᴀᴅᴇ&7 as you\n&7level up skills.\n&7\n&bMana Abilities &7are a special type\n&7of ability that requires activation\n&7and consumes &b&lᴍᴀɴᴀ&7.'
    abilities_click: '&a▶ &lᴄʟɪᴄᴋ &fto view.'
  stats_menu:
    stats_menu_title: Your Stats
    player_stat_entry: ' {color}{symbol} {stat} &f{level}'
    skills: '\n&7Skills: &f{skills}'
    your_level: '&7Your Level: {color}{level}'
    attack_damage: '&4+{value}% Attack Damage'
    hp: '&c+{value} ❤'
    saturated_regen: '&a+{value} Saturated Regen'
    full_hunger_regen: '&a+{value} Full Hunger Regen'
    almost_full_hunger_regen: '&a+{value} Almost Full Hunger Regen'
    mana_regen: '&b+{value} Mana/s Regen'
    luck: '&2+{value} Luck'
    double_drop_chance: '&2Double Drop Chance: {value}%'
    xp_gain: '&9+{value}% {xp_unit} Gain'
    anvil_cost_reduction: '&9Anvil Cost Reduction: {value}%'
    max_mana: '&9Max Mana: {value}'
    incoming_damage: '&5-{value}% Incoming Damage'
  unclaimed_items:
    unclaimed_items_title: 'Claim Items'
    inventory_full: '&eYou do not have enough space in your inventory to claim that item'
    click_to_claim: '&eClick to claim!'
  leaderboard:
    leaderboard_title: '&f:offset_-8::skills_leaderboard:'
    player_entry: '&a{place}. &f{player}'
    skill_level: '&7ʟᴇᴠᴇʟ: {level}'
  sources:
    sources_title: '&f:offset_-8::skills_xpsources:'
    sorter: '&a&lSort XP Sources'
    sort_type: '&7{selected}{type_name}\n'
    selected: '&a▶ '
    descending: 'Highest to Lowest'
    ascending: 'Lowest to Highest'
    alphabetical: 'Name (A-Z)'
    reverse_alphabetical: 'Name (Z-A)'
    sort_click: '&a▶ &lᴄʟɪᴄᴋ &fto sort.'
    source_name: '&f{name}'
    source_xp: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&aInformation:\n  &f• &fxᴘ: &d{xp}'
    source_xp_rate: '  &f• &fxᴘ: &d{xp}/{unit}'
    multiplied_xp: '\n  &f• &fᴍᴜʟᴛɪᴘʟɪᴇᴅ xᴘ: &d{xp}'
    multiplied_xp_rate: '\n  &f• &fᴍᴜʟᴛɪᴘʟɪᴇᴅ xᴘ: &d{xp}/{unit}'
    multiplied_desc: '\n &8ℹ This is the XP you will actually earn\n  &8   based on your current XP multipliers'
  abilities:
    abilities_title: '&f:offset_-8::skills_abilities:'
    locked_desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&aDescription:\n  &7{desc}'
    unlocked_at: '&7Unlocked at &b&l{skill} {level}'
    your_ability_level: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Your &f&ncurrent&7 level is &b&l{level}&7!'
    your_ability_level_maxed: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Your &f&ncurrent&7 level is &b&l{level}&7! &c(MAX!)'
    unlocked_desc: '&fNext upgrade at &3{skill} {level}&f\n  &7{desc}'
    unlocked_desc_maxed: '&aDescription:\n  &7{desc}'
    desc_upgrade_value: '&8{current}→&a{next}&7'
mana_abilities:
  replenish:
    name: 'ʀᴇᴘʟᴇɴɪsʜ'
    desc: '&7Replants crops &f&nautomatically&7 for \n&7   {value} seconds. &e[Right click hoe and\n&e   break crop to activate]'
    raise: '&7You raise your hoe'
    lower: '&7You lower your hoe'
    start: '&aReplenish Activated! &7(-{mana} {mana_unit})'
    end: '&7Replenish has worn off'
  treecapitator:
    name: 'ᴛʀᴇᴇᴄᴀᴘɪᴛᴀᴛᴏʀ'
    desc: '&7Breaks &aentire&7 trees instantly for \n&b   {value} seconds&7. &e[Right click axe and\n&e   break log to activate]'
    raise: '&7You raise your axe'
    lower: '&7You lower your axe'
    start: '&aTreecapitator Activated! &7(-{mana} {mana_unit})'
    end: '&7Treecapitator has worn off'
  speed_mine:
    name: 'sᴘᴇᴇᴅ ᴍɪɴᴇ'
    desc: '&7Gives &eHaste {haste_level}&7 for {value} seconds.\n&e   [Right click pickaxe and mine\n&e   block to activate]'
    raise: '&7You raise your pickaxe'
    lower: '&7You lower your pickaxe'
    start: '&aSpeed Mine Activated! &7(-{mana} {mana_unit})'
    end: '&7Speed Mine has worn off'
  absorption:
    name: 'ᴀʙsᴏʀᴘᴛɪᴏɴ'
    desc: 'Incoming damage will decrease &bmana\n&7   by &a2x&7 Minecraft damage instead of your\n&7   health for {value} seconds. Mana will not\n&7   regenerate while Absorption is active.\n   &e[Left click shield and take damage to activate]'
    raise: '&7You raise your shield'
    lower: '&7You lower your shield'
    start: '&aAbsorption Activated! &7(-{mana} {mana_unit})'
    end: '&7Absorption has worn off'
  terraform:
    name: 'ᴛᴇʀʀᴀғᴏʀᴍ'
    desc: 'Break blocks instantly in a 4 block radius\n&7   in the same layer when digging for {value} seconds. You\n&7   must use a shovel and extra blocks broken must be\n&7   the same type and in a single connected vein.\n&e   [Right click shovel and dig block to activate]'
    raise: '&7You raise your shovel'
    lower: '&7You lower your shovel'
    start: '&aTerraform Activated! &7(-{mana} {mana_unit})'
    end: '&7Terraform has worn off'
  sharp_hook:
    name: 'sʜᴀʀᴘ ʜᴏᴏᴋ'
    desc: 'Deal &c{value}&7 damage to a hooked entity\n&7   when left clicking with a fishing rod.'
    use: '&aUsed Sharp Hook! &7(-{mana} {mana_unit})'
    menu: '\n&d&lMana Ability &9{mana_ability} {level}\n  &7Damage: &c{value} &7Mana Cost: &b{mana_cost} &7Cooldown: &e{cooldown}s\n '
  charged_shot:
    name: 'ᴄʜᴀʀɢᴇᴅ sʜᴏᴛ'
    desc: 'Arrows you shoot will deal more damage\n&7   based on how far the bow was pulled back,\n&7   consuming &bmana&7 in the process. Does &a{value}%&7 more\n&7   damage per mana consumed. &e[Left click\n&e   a bow to toggle charged shot mode]'
    enable: '&7Charged shot mode is now enabled'
    disable: '&7Charged shot mode is now disabled'
    shoot: '&aUsed Charged Shot! &d+{percent}% DMG &7(-{mana} {mana_unit})'
    menu: '\n&d&lMana Ability &9{mana_ability} {level}\n  &7Damage: &c+{value}% / Mana &7Max Mana Cost: &b{mana_cost}\n '
  lightning_blade:
    name: 'ʟɪɢʜᴛɴɪɴɢ ʙʟᴀᴅᴇ'
    desc: 'Increases attack speed by &a{value}%&7 for {duration} seconds.\n&e   [Right click sword and attack mob to activate]'
    raise: '&7You ready your sword'
    lower: '&7You lower your sword'
    start: '&aLightning Blade Activated! &7(-{mana} {mana_unit})'
    end: '&7Lightning Blade has worn off'
    menu: '\n&d&lMana Ability &9{mana_ability} {level}\n  &7Attack Speed: &c+{value}% &7Duration: &a{duration}s\n  &7Mana Cost: &b{mana_cost} &7Cooldown: &e{cooldown}s\n '
  not_ready: '&cAbility not ready! &7({cooldown}s)'
  not_enough_mana: '&eNot enough mana! &7({mana} {mana_unit} required, you have {current_mana}/{max_mana})'
rewards:
  item:
    default_menu_message: '\n  {display_name}'
    default_menu_message_multiple: '\n  {display_name} {amount}x'
    default_chat_message: '\n  {display_name}'
    default_chat_message_multiple: '\n  {display_name} {amount}x'
skills:
  farming:
    name: Farming
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Harvest crops to &f&nearn&7 Farming {xp_unit}.'
  foraging:
    name: Foraging
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Cut trees to &f&nearn&7 Foraging {xp_unit}.'
  mining:
    name: Mining
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Mine stone and ores to &f&nearn&7 Mining {xp_unit}.'
  fishing:
    name: Fishing
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Catch fish to &f&nearn&7 Fishing {xp_unit}.'
  excavation:
    name: Excavation
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Dig with a shovel to &f&nearn&7 Excavation {xp_unit}.'
  archery:
    name: Archery
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Shoot mobs and players with a\n&7bow to &f&nearn&7 Archery {xp_unit}.'
  defense:
    name: Defense
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Take damage from entities\n&7to &f&nearn&7 Defense {xp_unit}.'
  fighting:
    name: Fighting
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Fight mobs with melee weapons\n&7to &f&nearn&7 Fighting {xp_unit}.'
  endurance:
    name: Endurance
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Walk and run to &f&nearn&7 Endurance {xp_unit}.'
  agility:
    name: Agility
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Jump and take fall damage\n&7to &f&nearn&7 Agility {xp_unit}.'
  alchemy:
    name: Alchemy
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Brew potions to &f&nearn&7 Alchemy {xp_unit}.'
  enchanting:
    name: Enchanting
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Enchant items and books\n&7to &f&nearn&7 Enchanting {xp_unit}.'
  sorcery:
    name: Sorcery
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Use &bmana abilities&7 to\n&f&nearn&7 Sorcery {xp_unit}.'
  healing:
    name: Healing
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Drink and splash potions\n&7to &f&nearn&7 Healing {xp_unit}.'
  forging:
    name: Forging
    desc: '&7Combine and apply books in an\n&7anvil to &f&nearn&7 Forging {xp_unit}.'
sources:
  farming:
    wheat: Wheat
    potato: Potato
    carrot: Carrot
    beetroot: Beetroot
    nether_wart: Nether Wart
    pumpkin: Pumpkin
    melon: Melon
    sugar_cane: Sugar Cane
    bamboo: Bamboo
    cocoa: Cocoa
    cactus: Cactus
    brown_mushroom: Brown Mushroom
    red_mushroom: Red Mushroom
    kelp: Kelp
    sea_pickle: Sea Pickles
    sweet_berry_bush: Sweet Berries
    glow_berries: Glow Berries
    torchflower: Torchflower
    pitcher_plant: Pitcher Plant
  foraging:
    oak_log: Oak Log
    spruce_log: Spruce Log
    birch_log: Birch Log
    jungle_log: Jungle Log
    acacia_log: Acacia Log
    dark_oak_log: Dark Oak Log
    oak_leaves: Oak Leaves
    spruce_leaves: Spruce Leaves
    birch_leaves: Birch Leaves
    jungle_leaves: Jungle Leaves
    acacia_leaves: Acacia Leaves
    dark_oak_leaves: Dark Oak Leaves
    crimson_stem: Crimson Stem
    warped_stem: Warped Stem
    nether_wart_block: Nether Wart Block
    warped_wart_block: Warped Wart Block
    moss_block: Moss Block
    moss_carpet: Moss Carpet
    azalea: Azalea
    flowering_azalea: Flowering Azalea
    azalea_leaves: Azalea Leaves
    flowering_azalea_leaves: Flowering Azalea Leaves
    mangrove_log: Mangrove Log
    mangrove_leaves: Mangrove Leaves
    mangrove_roots: Mangrove Roots
    cherry_log: Cherry Log
    cherry_leaves: Cherry Leaves
    pink_petals: Pink Petals
  mining:
    stone: Stone
    cobblestone: Cobblestone
    andesite: Andesite
    diorite: Diorite
    granite: Granite
    coal_ore: Coal Ore
    nether_quartz_ore: Nether Quartz Ore
    iron_ore: Iron Ore
    lapis_ore: Lapis Ore
    redstone_ore: Redstone Ore
    gold_ore: Gold Ore
    diamond_ore: Diamond Ore
    emerald_ore: Emerald Ore
    terracotta: Terracotta
    white_terracotta: White Terracotta
    orange_terracotta: Orange Terracotta
    yellow_terracotta: Yellow Terracotta
    light_gray_terracotta: Light Gray Terrcotta
    brown_terracotta: Brown Terrcotta
    red_terracotta: Red Terracotta
    netherrack: Netherrack
    blackstone: Blackstone
    basalt: Basalt
    magma_block: Magma Block
    nether_gold_ore: Nether Gold Ore
    ancient_debris: Ancient Debris
    end_stone: End Stone
    obsidian: Obsidian
    deepslate: Deepslate
    copper_ore: Copper Ore
    tuff: Tuff
    calcite: Calcite
    smooth_basalt: Smooth Basalt
    amethyst_block: Amethyst Block
    amethyst_cluster: Amethyst Cluster
    deepslate_coal_ore: Deepslate Coal Ore
    deepslate_iron_ore: Deepslate Iron Ore
    deepslate_copper_ore: Deepslate Copper Ore
    deepslate_gold_ore: Deepslate Gold Ore
    deepslate_redstone_ore: Deepslate Redstone Ore
    deepslate_lapis_ore: Deepslate Lapis Ore
    deepslate_emerald_ore: Deepslate Emerald Ore
    deepslate_diamond_ore: Deepslate Diamond Ore
    dripstone_block: Dripstone Block
    ice: Ice
    packed_ice: Packed Ice
    blue_ice: Blue Ice
    reinforced_deepslate: Reinforced Deepslate
  fishing:
    cod: Cod
    salmon: Salmon
    tropical_fish: Tropical Fish
    pufferfish: Pufferfish
    treasure: Treasure Items
    junk: Junk Items
    rare: Rare Loot
    epic: Epic Loot
  excavation:
    dirt: Dirt
    grass_block: Grass Block
    sand: Sand
    gravel: Gravel
    mycelium: Mycelium
    clay: Clay
    soul_sand: Soul Sand
    coarse_dirt: Coarse Dirt
    podzol: Podzol
    soul_soil: Soul Soil
    red_sand: Red Sand
    rooted_dirt: Rooted Dirt
    mud: Mud
    muddy_mangrove_roots: Muddy Mangrove Roots
  mobs:
    player: Player
    bat: Bat
    cat: Cat
    chicken: Chicken
    cod: Cod
    cow: Cow
    donkey: Donkey
    fox: Fox
    giant: Giant
    horse: Horse
    mooshroom: Mooshroom
    mule: Mule
    ocelot: Ocelot
    parrot: Parrot
    pig: Pig
    rabbit: Rabbit
    salmon: Salmon
    sheep: Sheep
    skeleton_horse: Skeleton Horse
    snow_golem: Snow Golem
    squid: Squid
    strider: Strider
    tropical_fish: Tropical Fish
    turtle: Turtle
    villager: Villager
    wandering_trader: Wandering Trader
    bee: Bee
    cave_spider: Cave Spider
    dolphin: Dolphin
    enderman: Enderman
    iron_golem: Iron Golem
    llama: Llama
    piglin: Piglin
    panda: Panda
    polar_bear: Polar Bear
    pufferfish: Pufferfish
    spider: Spider
    wolf: Wolf
    zombified_piglin: Zombified Piglin
    blaze: Blaze
    creeper: Creeper
    drowned: Drowned
    elder_guardian: Elder Guardian
    endermite: Endermite
    evoker: Evoker
    ghast: Ghast
    guardian: Guardian
    hoglin: Hoglin
    husk: Husk
    illusioner: Illusioner
    magma_cube: Magma Cube
    phantom: Phantom
    piglin_brute: Piglin Brute
    pillager: Pillager
    ravager: Ravager
    shulker: Shulker
    silverfish: Silverfish
    skeleton: Skeleton
    slime: Slime
    stray: Stray
    vex: Vex
    vindicator: Vindicator
    witch: Witch
    wither_skeleton: Wither Skeleton
    zoglin: Zoglin
    zombie: Zombie
    zombie_villager: Zombie Villager
    ender_dragon: Ender Dragon
    wither: Wither
    axolotl: Axolotl
    glow_squid: Glow Squid
    goat: Goat
    allay: Allay
    frog: Frog
    tadpole: Tadpole
    warden: Warden
    camel: Camel
    sniffer: Sniffer
  defense:
    mob_damage: Damage from Mobs
    player_damage: Damage from Players
  endurance:
    walk_per_meter: Walking
    sprint_per_meter: Sprinting
    swim_per_meter: Swimming
  agility:
    jump_per_100: Jumping
    fall_damage: Taking Fall Damage
  alchemy:
    awkward: Awkward Potion
    regular: Regular Potion
    extended: Extended Duration Potion
    upgraded: Upgraded Potion
    splash: Splash Potion
    lingering: Lingering Potion
  enchanting:
    weapon_per_level: Weapon
    armor_per_level: Armor
    tool_per_level: Tool
    book_per_level: Book
  sorcery:
    mana_ability_use: Using Mana Abilities
    sculk: Sculk
    sculk_vein: Sculk Vein
    sculk_sensor: Sculk Sensor
    sculk_catalyst: Sculk Catalyst
    sculk_shrieker: Sculk Shrieker
  healing:
    drink_regular: Drink Regular Potion
    drink_extended: Drink Extended Duration Potion
    drink_upgraded: Drink Upgraded Potion
    splash_regular: Splash Regular Potion
    splash_extended: Splash Extended Duration Potion
    splash_upgraded: Splash Upgraded Potion
    lingering_regular: Use Regular Lingering Potion
    lingering_extended: Use Extended Duration Lingering Potion
    lingering_upgraded: Use Upgraded Lingering Potion
    golden_apple: Golden Apple
    enchanted_golden_apple: Enchanted Golden Apple
  forging:
    combine_books_per_level: Combine Enchanted Books
    combine_weapon_per_level: Combine Weapon with Book
    combine_armor_per_level: Combine Armor with Book
    combine_tool_per_level: Combine Tool with Book
    grindstone_per_level: Disenchant Item with Grindstone
  units:
    damage: Damage
    meter: Meter
    100_jumps: 100 Jumps
    mana: Mana Consumed
    combine_level: Anvil Cost XP
    grindstone_level: Level of Enchant on Item
    enchant_level: Level of Enchant Received
stats:
  strength:
    name: '&fsᴛʀᴇɴɢʜᴛ'
    desc: '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ\n&7\n&7Strength increases your attack\n&cdamage &7with various different\n&7weapons'
    color: '&4'
    symbol: '➽'
  health:
    name: '&fʜᴇᴀʟᴛʜ'
    desc: '&7Health increases the amount of {hp_unit} you\n&7have, allowing you to last longer in\n&7fights'
    color: '&c'
    symbol: '❤'
  regeneration:
    name: '&fʀᴇɢᴇɴᴇʀᴀᴛɪᴏɴ'
    desc: '&7Regeneration increases how fast you\n&7recover both health and mana'
    color: '&a'
    symbol: '❥'
  luck:
    name: '&fʟᴜᴄᴋ'
    desc: '&7Luck increases your chances of getting\n&7rare loot from mobs, fishing, and more'
    color: '&2'
    symbol: '☘'
  wisdom:
    name: '&fᴡɪsᴅᴏᴍ'
    desc: '&7Wisdom increases your max mana, increases\n&7experience gain, and decreases anvil costs'
    color: '&9'
    symbol: '✿'
  toughness:
    name: '&fᴛᴏᴜɢʜɴᴇss'
    desc: '&7Toughness increases the amount of\n&7damage reduced from enemy attacks'
    color: '&5'
    symbol: '✦'
units:
  mana: Mana
  hp: HP
  xp: XP
file_version: 30

# 184C9YDJB87VI9POW