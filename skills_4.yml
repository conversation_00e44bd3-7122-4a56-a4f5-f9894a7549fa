menu_title: '&f:offset_-8::skills_4:'
open_commands:
  - '[sound] BLOCK_NOTE_BLOCK_PLING'
size: 54
update_interval: 1
items:
  '1':
    material: PAPER
    model_data: 1010
    display_name: '&a&lAgility &a%aureliumskills_agility_roman%'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Jump and take fall damage'
      - '&7to &f&nearn&7 Agility XP.'
      - ''
      - '&aStats Leveled:'
      - ' &9 ✿ &fᴡɪsᴅᴏᴍ'
      - ' &6 ❥ &fʀᴇɢᴇɴᴇʀᴀᴛɪᴏɴ'
      - ''
      - '&aInformation:'
      - '  &f• &fʟᴇᴠᴇʟ: &a%aureliumskills_agility_roman%'
      - '  &f• &fᴘʀᴏɢʀᴇss: &d%aureliumskills_xp_int_agility%/%aureliumskills_xp_required_agility% &8(%aureliumskills_xp_progress_1_agility%%)'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view your progression.'
    left_click_commands:
      - '[player] agility'
    right_click_commands:
      - '[player] agility'
    slot: 19
  '2':
    material: PAPER
    model_data: 1011
    display_name: '&a&lAlchemy &a%aureliumskills_alchemy_roman%'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Brew potions to &f&nearn&7 Alchemy XP.'
      - ''
      - '&aStats Leveled:'
      - ' &c ❤ &fʜᴇᴀʟᴛʜ'
      - ' &9 ✿ &fᴡɪsᴅᴏᴍ'
      - ''
      - '&aInformation:'
      - '  &f• &fʟᴇᴠᴇʟ: &a%aureliumskills_alchemy_roman%'
      - '  &f• &fᴘʀᴏɢʀᴇss: &d%aureliumskills_xp_int_alchemy%/%aureliumskills_xp_required_alchemy% &8(%aureliumskills_xp_progress_1_alchemy%%)'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view your progression.'
    left_click_commands:
      - '[player] alchemy'
    right_click_commands:
      - '[player] alchemy'
    slot: 22
  '3':
    material: PAPER
    model_data: 1012
    display_name: '&a&lEnchanting &a%aureliumskills_enchanting_roman%'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Enchant items and books'
      - '&7to &f&nearn&7 Enchanting XP.'
      - ''
      - '&aStats Leveled:'
      - ' &9 ✿ &fᴡɪsᴅᴏᴍ'
      - ' &2 ☘ &fʟᴜᴄᴋ'
      - ''
      - '&aInformation:'
      - '  &f• &fʟᴇᴠᴇʟ: &a%aureliumskills_enchanting_roman%'
      - '  &f• &fᴘʀᴏɢʀᴇss: &d%aureliumskills_xp_int_enchanting%/%aureliumskills_xp_required_enchanting% &8(%aureliumskills_xp_progress_1_enchanting%%)'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view your progression.'
    left_click_commands:
      - '[player] enchanting'
    right_click_commands:
      - '[player] enchanting'
    slot: 25
  'prev_page':
    material: MAP
    model_data: 1000
    slot: 36
    priority: 1
    update: true
    display_name: '&a&lPrevious Page'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[openguimenu] skills_3'
    right_click_commands:
      - '[openguimenu] skills_3'
  'next_page':
    material: MAP
    model_data: 1000
    slot: 44
    priority: 1
    update: true
    display_name: '&a&lNext Page'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[openguimenu] skills_5'
    right_click_commands:
      - '[openguimenu] skills_5'
  'skills':
    material: LEGACY_BOOK_AND_QUILL
    slot: 47
    priority: 1
    update: true
    display_name: '&a&lYour Skills'
    lore:
      - '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ'
      - ''
      - '&7Upgrade &b&lsᴋɪʟʟs &7by doing various'
      - '&7tasks to unlock valuable stat boosts,'
      - '&7abilities and more!'
      - ''
    left_click_commands:
      - '[none]'
    right_click_commands:
      - '[none]'
  'close':
    material: MAP
    model_data: 1000
    slot: 49
    priority: 1
    update: true
    display_name: '&c&lClose'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[close]'
    right_click_commands:
      - '[close]'
  'stats':
    material: head-%player_name%
    slot: 51
    priority: 1
    update: true
    display_name: '&a&lYour Stats'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Earn stat &dbuffs &7by'
      - '&7level up skills!'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view.'
    left_click_commands:
      - '[openguimenu] skills_stats_1'
    right_click_commands:
      - '[openguimenu] skills_stats_1'