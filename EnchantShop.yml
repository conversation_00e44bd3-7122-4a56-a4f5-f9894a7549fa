open_command:
  - eshop
  - enchantshop
  - es
open_commands:
  - '[sound] BLOCK_COMPARATOR_CLICK'
register_command: true
size: 53

menu_title: '&fऱऩलम'

items:
  'Tool Orb':
    material: PAPER
    model_data: 8
    slot: 10
    display_name: "&fय Enchantment Orb"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 10000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 10000'
      - '[console] ae giveitem %player_name% orb 1 TOOL 1 100'
      - "[message] &fए&#00d52a You just bought &fय Enchantment Orb"
    lore:
      - ''
      - '&#dff9fbOrb oncreases the slots of'
      - '&#dff9fbenchantment on a tool.'
      - ''
      - '&a+&f ʙᴜʏ ᴘʀɪᴄᴇ:&7 10.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Weapon  Orb':
    material: PAPER
    model_data: 7
    slot: 11
    display_name: "&fय Enchantment Orb"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 10000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 10000'
      - '[console] ae giveitem %player_name% orb 1 WEAPON 1 100'
      - "[message] &fए&#00d52a You just bought &fय Enchantment Orb"
    lore:
      - ''
      - '&#dff9fbOrb oncreases the slots of'
      - '&#dff9fbenchantment on a weapon.'
      - ''
      - '&a+&f ʙᴜʏ ᴘʀɪᴄᴇ:&7 10.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Armor  Orb':
    material: PAPER
    model_data: 6
    slot: 12
    display_name: "&fय Enchantment Orb"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 10000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 10000'
      - '[console] ae giveitem %player_name% orb 1 ARMOR 1 100'
      - "[message] &fए&#00d52a You just bought &fय Enchantment Orb"
    lore:
      - ''
      - '&#dff9fbOrb oncreases the slots of'
      - '&#dff9fbenchantment on a armor.'
      - ''
      - '&a+&f ʙᴜʏ ᴘʀɪᴄᴇ:&7 10.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'



  'Soul Tracker':
    material: PAPER
    model_data: 17
    slot: 13
    display_name: "&fय Soul Tracker"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 5000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 5000'
      - '[console] ae giveitem %player_name% soultracker 1'
      - "[message] &fए&#00d52a You just bought &fय Soul Tracker"
    lore:
      - ''
      - '&#dff9fbApply to weapons to start'
      - '&#dff9fbtracking souls collected '
      - '&#dff9fbfrom kills.'
      - ''
      - '&a+&f ʙᴜʏ ᴘʀɪᴄᴇ:&7 5.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Mob Kill Tracker':
    material: PAPER
    model_data: 10
    slot: 14
    display_name: "&fय Mob Kill Tracker"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 5000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 5000'
      - '[console] ae giveitem %player_name% mobtrak 1'
      - "[message] &fए&#00d52a You just bought &fय Mob Kill Tracker"
    lore:
      - ''
      - '&#dff9fbTrack how many mob kills'
      - '&#dff9fbyou have with a weapon.'
      - ''
      - '&a+&f ʙᴜʏ ᴘʀɪᴄᴇ:&7 5.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'


  'Block Tracker':
    material: PAPER
    model_data: 11
    slot: 15
    display_name: "&fय Block Tracker"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 5000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 5000'
      - '[console] ae giveitem %player_name% blocktrak 1'
      - "[message] &fए&#00d52a You just bought &fय Block Tracker"
    lore:
      - ''
      - '&#dff9fbTrack how many blocks you'
      - '&#dff9fbbreak w/ a tool drag ontop'
      - '&#dff9fbof another item to apply it'
      - ''
      - '&a+&f ʙᴜʏ ᴘʀɪᴄᴇ:&7 5.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Kill Tracker':
    material: PAPER
    model_data: 9
    slot: 16
    display_name: "&fय Player Kill Tracker"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 5000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 5000'
      - '[console] ae giveitem %player_name% stattrak 1'
      - "[message] &fए&#00d52a You just bought &fय Player Kill Tracker"
    lore:
      - ''
      - '&#dff9fbTrack how many kills you have'
      - '&#dff9fbwith a weapon  drag  ontop of'
      - '&#dff9fbanother item to apply it'
      - ''
      - '&a+&f ʙᴜʏ ᴘʀɪᴄᴇ:&7 5.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Protection Scroll':
    material: PAPER
    model_data: 12
    slot: 19
    display_name: "&fय Protection Scroll"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 60000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 60000'
      - '[console] ae giveitem %player_name% whitescroll 1'
      - "[message] &fए&#00d52a You just bought &fय Protection Scroll"
    lore:
      - ''
      - '&#dff9fbPrevents an item from being'
      - '&#dff9fbdestroyed due to a failed'
      - '&#dff9fbEnchantment Book.'
      - ''
      - '&a+&f ʙᴜʏ ᴘʀɪᴄᴇ:&7 60.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Soulbound Scroll':
    material: PAPER
    model_data: 15
    slot: 20
    display_name: "&fय Soulbound Scroll"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 50000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 50000'
      - '[console] ae giveitem %player_name% holywhitescroll 1'
      - "[message] &fए&#00d52a You just bought &fय Soulbond Scroll"
    lore:
      - ''
      - '&#dff9fbA legendary reward that can'
      - '&#dff9fbbe applied to armor/weapons,'
      - '&#dff9fbgives a &c% &#dff9fbchance of not'
      - '&#dff9fblosing the blessed item when'
      - '&#dff9fbyou die.'
      - ''
      - '&a+&f ʙᴜʏ ᴘʀɪᴄᴇ:&7 50.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Removal Scroll':
    material: PAPER
    model_data: 13
    slot: 21
    display_name: "&fय Removal Scroll"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 15000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 15000'
      - '[console] ae giveitem %player_name% blackscroll 1 50'
      - "[message] &fए&#00d52a You just bought &fय Removal Scroll"
    lore:
      - ''
      - '&#dff9fbRemoves a random enchantment'
      - '&#dff9fbfrom an item  & converts it into'
      - '&#dff9fba &fRandom% &#dff9fbsuccess book.'
      - ''
      - '&a+&f ʙᴜʏ ᴘʀɪᴄᴇ:&7 15.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Transmog Scroll':
    material: PAPER
    model_data: 16
    slot: 22
    display_name: "&fय Transmog Scroll"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 5000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 5000'
      - '[console] ae giveitem %player_name% transmog 1 50'
      - "[message] &fए&#00d52a You just bought &fय Transmog Scroll"
    lore:
      - ''
      - '&#dff9fbOrganizes enchants amount'
      - '&#dff9fbby &erarity &#dff9fbon item and adds'
      - '&#dff9fbthe &dlore &bcount&#dff9fb to name.'
      - ''
      - '&a+&f ʙᴜʏ ᴘʀɪᴄᴇ:&7 5.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Soul Gem':
    material: PAPER
    model_data: 2
    slot: 24
    display_name: "&fय Soul Gem"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 13000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 13000'
      - '[console] ae giveitem %player_name% soulgem 1 50'
      - "[message] &fए&#00d52a You just bought &fय Soul Gem"
    lore:
      - ''
      - '&#dff9fbsᴏᴜʟ ᴀᴍᴏᴜɴᴛ: &#b434fc50&fऌ'
      - ''
      - '&a+&f ʙᴜʏ ᴘʀɪᴄᴇ:&7 13.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'

  'Rename Quill':
    material: PAPER
    model_data: 1
    slot: 25
    display_name: "&fय Rename Quill"
    click_requirement:
      requirements:
        hasExp:
          type: has exp
          amount: 25000
          level: false
          deny_commands:
          - "[message] &fऎ &#e55039You dont have enought EXP to buy this item."
    click_commands:
      - '[takeexp] 25000'
      - '[console] ae giveitem %player_name% renametag 1'
      - "[message] &fए&#00d52a You just bought &fय Rename Quill"
    lore:
      - ''
      - '&#dff9fbsᴏᴜʟ ᴀᴍᴏᴜɴᴛ: &#b434fc50&fऌ'
      - ''
      - '&a+&f ʙᴜʏ ᴘʀɪᴄᴇ:&7 25.000 EXP'
      - ''
      - '&7ऐ ᴛᴏ ʙᴜʏ ᴛʜɪs ɪᴛᴇᴍ ɴᴏᴡ'


  'Back':
    material: PAPER
    model_data: 99
    slot: 49
    display_name: "&#e55039ʙᴀᴄᴋ ᴛᴏ ᴇɴᴄʜᴀɴᴛᴇʀ"
    click_commands:
      - '[sound] BLOCK_COMPARATOR_CLICK'
      - '[player] enchanter'
  'Previous':
    material: PAPER
    model_data: 99
    slots: [47,48]
    display_name: "&#f0932bᴘʀᴇᴠɪᴏᴜs ᴘᴀɢᴇ"
    lore:
      - '&fᴘᴀɢᴇ: &71/2'
  'Next':
    material: PAPER
    model_data: 99
    slots: [50,51]
    display_name: "&#00d52aɴᴇxᴛ ᴘᴀɢᴇ"
    lore:
      - '&fᴘᴀɢᴇ: &71/2'
    click_commands:
      - '[openguimenu] EnchantShopNext'