menu_title: '&f:offset_-8::skills_2:'
open_commands:
  - '[sound] BLOCK_NOTE_BLOCK_PLING'
size: 54
update_interval: 1
items:
  '1':
    material: PAPER
    model_data: 1004
    display_name: '&a&lFishing &a%aureliumskills_fishing_roman%'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Cath fish to &f&nearn&7 Fishing XP.'
      - ''
      - '&aStats Leveled:'
      - ' &2 ☘ &fʟᴜᴄᴋ'
      - ' &c ❤ &fʜᴇᴀʟᴛʜ'
      - ''
      - '&aInformation:'
      - '  &f• &fʟᴇᴠᴇʟ: &a%aureliumskills_fishing_roman%'
      - '  &f• &fᴘʀᴏɢʀᴇss: &d%aureliumskills_xp_int_fishing%/%aureliumskills_xp_required_fishing% &8(%aureliumskills_xp_progress_1_fishing%%)'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view your progression.'
    left_click_commands:
      - '[player] fishing'
    right_click_commands:
      - '[player] fishing'
    slot: 19
  '2':
    material: PAPER
    model_data: 1005
    display_name: '&a&lExcavation &a%aureliumskills_excavation_roman%'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Dig with a shovel to &f&nearn&7 Excavation XP.'
      - ''
      - '&aStats Leveled:'
      - ' &6 ❥ &fʀᴇɢᴇɴᴇʀᴀᴛɪᴏɴ'
      - ' &2 ☘ &fʟᴜᴄᴋ'
      - ''
      - '&aInformation:'
      - '  &f• &fʟᴇᴠᴇʟ: &a%aureliumskills_excavation_roman%'
      - '  &f• &fᴘʀᴏɢʀᴇss: &d%aureliumskills_xp_int_excavation%/%aureliumskills_xp_required_excavation% &8(%aureliumskills_xp_progress_1_excavation%%)'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view your progression.'
    left_click_commands:
      - '[player] excavation'
    right_click_commands:
      - '[player] excavation'
    slot: 22
  '3':
    material: PAPER
    model_data: 1006
    display_name: '&a&lArchery &a%aureliumskills_archery_roman%'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Shoot mobs and players with a'
      - '&7bow to &f&nearn&7 Archery XP.'
      - ''
      - '&aStats Leveled:'
      - ' &2 ☘ &fʟᴜᴄᴋ'
      - ' &4 ➽ &fsᴛʀᴇɴɢʜᴛ'
      - ''
      - '&aInformation:'
      - '  &f• &fʟᴇᴠᴇʟ: &a%aureliumskills_archery_roman%'
      - '  &f• &fᴘʀᴏɢʀᴇss: &d%aureliumskills_xp_int_archery%/%aureliumskills_xp_required_archery% &8(%aureliumskills_xp_progress_1_archery%%)'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view your progression.'
    left_click_commands:
      - '[player] archery'
    right_click_commands:
      - '[player] archery'
    slot: 25
  'prev_page':
    material: MAP
    model_data: 1000
    slot: 36
    priority: 1
    update: true
    display_name: '&a&lPrevious Page'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[openguimenu] skills_1'
    right_click_commands:
      - '[openguimenu] skills_1'
  'next_page':
    material: MAP
    model_data: 1000
    slot: 44
    priority: 1
    update: true
    display_name: '&a&lNext Page'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[openguimenu] skills_3'
    right_click_commands:
      - '[openguimenu] skills_3'
  'skills':
    material: LEGACY_BOOK_AND_QUILL
    slot: 47
    priority: 1
    update: true
    display_name: '&a&lYour Skills'
    lore:
      - '&8ɪɴғᴏʀᴍᴀᴛɪᴠᴇ'
      - ''
      - '&7Upgrade &b&lsᴋɪʟʟs &7by doing various'
      - '&7tasks to unlock valuable stat boosts,'
      - '&7abilities and more!'
      - ''
    left_click_commands:
      - '[none]'
    right_click_commands:
      - '[none]'
  'close':
    material: MAP
    model_data: 1000
    slot: 49
    priority: 1
    update: true
    display_name: '&c&lClose'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
    left_click_commands:
      - '[close]'
    right_click_commands:
      - '[close]'
  'stats':
    material: head-%player_name%
    slot: 51
    priority: 1
    update: true
    display_name: '&a&lYour Stats'
    lore:
      - '&8ɪɴᴛᴇʀᴀᴄᴛɪᴠᴇ'
      - ''
      - '&7Earn stat &dbuffs &7by'
      - '&7level up skills!'
      - ''
      - '&a▶ &lᴄʟɪᴄᴋ &fto view.'
    left_click_commands:
      - '[openguimenu] skills_stats_1'
    right_click_commands:
      - '[openguimenu] skills_stats_1'